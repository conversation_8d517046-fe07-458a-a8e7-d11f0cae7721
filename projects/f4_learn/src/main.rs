#![no_std]
#![no_main]

use defmt::info;
use embassy_executor::Spawner;
use embassy_stm32::i2c::{self, Config as I2cConfig, I2c};
use embassy_stm32::rcc::{APBPrescaler, Hse, HseMode, Pll, PllMul, PllPreDiv, Sysclk};
use embassy_stm32::time::Hertz;
use embassy_stm32::{Config, bind_interrupts, peripherals};
use embassy_time::{Delay, Timer};
use embedded_hal::digital::{ErrorType as DigitalError, InputPin, OutputPin};
use heapless::String;
use icm20948_async::{AccR<PERSON><PERSON>, GyrDlp, GyrUnit, Icm20948};
use {defmt_rtt as _, panic_probe as _};

bind_interrupts!(struct Irqs {
    I2C2_EV => i2c::EventInterruptHandler<peripherals::I2C2>;
    I2C2_ER => i2c::ErrorInterruptHandler<peripherals::I2C2>;
});

#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>)]
pub struct NoPin;

impl DigitalError for NoPin {
    type Error = core::convert::Infallible;
}

// 空输出引脚：写高写低都不做事
impl OutputPin for NoPin {
    fn set_low(&mut self) -> Result<(), Self::Error> {
        Ok(())
    }
    fn set_high(&mut self) -> Result<(), Self::Error> {
        Ok(())
    }
}

// 空输入引脚：总是返回 false
impl InputPin for NoPin {
    fn is_high(&mut self) -> Result<bool, Self::Error> {
        Ok(false)
    }
    fn is_low(&mut self) -> Result<bool, Self::Error> {
        Ok(true)
    }
}

fn init() -> Config {
    // STM32F407VET6 的 HSE 晶振通常为 8MHz
    let mut config = Config::default();

    // 外部高速晶振 (HSE) 配置
    // 这里的配置假设你使用的开发板上有 8MHz 的外部晶振。
    config.rcc.hse = Some(Hse {
        freq: Hertz::mhz(8),
        mode: HseMode::Oscillator,
    });

    // PLL (锁相环) 配置
    // PLL 用于倍频 HSE 产生高频时钟
    // STM32F407VET6 的最大系统时钟 (SYSCLK) 为 168MHz。
    config.rcc.pll = Some(Pll {
        // PLLM: HSE 时钟分频器，用于将 HSE 时钟分频到 1MHz
        // PLLM = 8MHz / 8 = 1MHz。这是 PLL 的输入时钟 (PLL_VCO_IN)
        // 根据数据手册，PLL_VCO_IN 必须在 1-2MHz 之间
        prediv: PllPreDiv::DIV8,
        // PLLN: PLL 倍频系数
        // PLLN = 168。PLL_VCO_OUT = PLL_VCO_IN * PLLN = 1MHz * 168 = 168MHz
        // PllMul::MUL168 等于设置 PLLN = 168
        mul: PllMul::MUL168,
        // PLLP: 系统时钟分频器，用于产生 SYSCLK
        // PLLP = 2。SYSCLK = PLL_VCO_OUT / PLLP = 168MHz / 2 = 84MHz
        // `embassy-stm32` 的 `Sysclk::PLL1_P` 会自动处理 PLLP
        divp: Some(embassy_stm32::rcc::PllPDiv::DIV2),
        // PLLQ: USB/SDIO/RNG 时钟分频器
        // USB FS 需要 48MHz 时钟。PLL_VCO_OUT / 3.5 = 168 / 3.5 = 48MHz。
        // 但是 `PllQDiv` 的分频系数是整数，通常我们会选择 PllMul 为 336，然后 PllQDiv 为 7。
        // 或者，更常见的做法是直接配置到 168MHz，然后 USB 使用额外的分频器。
        // 在这里我们先忽略 USB 的配置，只确保 SYSCLK 正确。
        divq: None,
        // PLLR: 主要用于某些高级型号，F407 一般不需要
        divr: None,
    });

    // 系统时钟 (SYSCLK) 的来源选择 PLL
    config.rcc.sys = Sysclk::PLL1_P;

    // APB1 总线预分频器
    // APB1 总线的最大频率为 42MHz
    // SYSCLK (168MHz) -> HCLK (168MHz) -> APB1 (168MHz / 4 = 42MHz)
    config.rcc.apb1_pre = APBPrescaler::DIV4;

    // APB2 总线预分频器
    // APB2 总线的最大频率为 84MHz
    // SYSCLK (168MHz) -> HCLK (168MHz) -> APB2 (168MHz / 2 = 84MHz)
    config.rcc.apb2_pre = APBPrescaler::DIV2;
    config
}

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    // 初始化 Embassy 并应用配置
    let config = init();
    let p = embassy_stm32::init(config);

    let i2c_bus = I2c::new(
        p.I2C2,
        p.PB10,
        p.PB11,
        Irqs,
        p.DMA1_CH7,
        p.DMA1_CH2,
        Hertz(100_000),
        I2cConfig::default(),
    );

    info!("0");
    let mut imu = Icm20948::new_i2c(i2c_bus, Delay)
        .gyr_unit(GyrUnit::Rps) // Set gyroscope to output rad/s
        .gyr_dlp(GyrDlp::Hz196) // Set gyroscope low-pass filter
        .acc_range(AccRange::Gs8) // Set accelerometer measurement range
        .set_address(0x68) // Set address (0x68 or 0x69)
        .initialize_9dof() // Initialize with magnetometer
        .await
        .unwrap();
    info!("0");

    loop {
        // 1. 读取 IMU 数据
        let measurement = imu.read_9dof().await.unwrap();
        let ax = measurement.acc.x;
        let ay = measurement.acc.y;
        let az = measurement.acc.z;
        let gx = measurement.gyr.x;
        let gy = measurement.gyr.y;
        let gz = measurement.gyr.z;

        // 3. 将浮点数格式化为字符串
        // 使用 `heapless::String` 作为缓冲区，避免动态内存分配
        let mut acc_str: String<32> = String::new();
        let mut gyr_str: String<32> = String::new();

        use core::fmt::Write;
        write!(acc_str, "A: {:.2},{:.2},{:.2}", ax, ay, az).unwrap();
        write!(gyr_str, "G: {:.2},{:.2},{:.2}", gx, gy, gz).unwrap();

        info!("{}", acc_str);
        info!("{}", gyr_str);

        Timer::after_millis(1).await;
    }
}
