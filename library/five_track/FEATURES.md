# 五路循迹模块库功能特性

## 核心功能

### 1. 传感器支持
- ✅ 支持模拟传感器 (ADC)
- ✅ 支持数字传感器 (GPIO)
- ✅ 可配置的传感器阈值
- ✅ 传感器反转支持
- ✅ 滤波器支持（移动平均）

### 2. 数据结构
- ✅ `SensorType` - 传感器类型枚举
- ✅ `SensorState` - 传感器状态（黑线/白线/错误）
- ✅ `SensorPosition` - 传感器位置枚举
- ✅ `SensorReadings` - 传感器读数结构
- ✅ `SensorConfig` - 传感器配置
- ✅ `CalibrationData` - 校准数据

### 3. PID控制
- ✅ `PidController` - PID控制器实现
- ✅ `PidConfig` - PID参数配置
- ✅ 比例、积分、微分控制
- ✅ 积分限幅和输出限幅
- ✅ 自动时间间隔计算

### 4. 循迹算法
- ✅ 线路位置计算（加权平均）
- ✅ 交叉路口检测
- ✅ 线路丢失检测
- ✅ 急转弯检测
- ✅ 搜索模式（线路丢失时）

### 5. 校准功能
- ✅ 自动校准过程
- ✅ 手动校准数据设置
- ✅ 传感器读数标准化
- ✅ 校准数据持久化支持

### 6. 异步支持
- ✅ 基于Embassy框架的异步API
- ✅ `SensorReader` trait异步接口
- ✅ `TrackingTask` 异步任务封装
- ✅ 非阻塞传感器读取

### 7. 错误处理
- ✅ `TrackError` 错误类型定义
- ✅ `ErrorHandler` 错误处理器
- ✅ `ErrorRecoveryStrategy` 恢复策略
- ✅ 错误统计和重试机制

### 8. 数据记录和分析
- ✅ `SensorDataLogger` 数据记录器
- ✅ `SensorStatistics` 统计信息
- ✅ 历史数据缓存
- ✅ 数据稳定性检查

### 9. 实用工具
- ✅ 传感器状态字符串转换
- ✅ 变化率计算
- ✅ 读数平滑处理
- ✅ 稳定性检查

### 10. 配置系统
- ✅ 模块化配置结构
- ✅ 默认配置支持
- ✅ 运行时配置更新
- ✅ 特性门控编译

## 技术特性

### 编译特性
- ✅ `no_std` 支持
- ✅ 条件编译支持
- ✅ 可选依赖管理
- ✅ 跨平台兼容性

### 性能特性
- ✅ 零分配设计（使用heapless）
- ✅ 高效的数据结构
- ✅ 最小化内存占用
- ✅ 实时性能保证

### 调试特性
- ✅ defmt日志支持
- ✅ 详细的调试信息
- ✅ 状态可视化
- ✅ 性能监控

## 使用场景

### 1. 基本循迹
```rust
let mut controller = FiveTrackController::new(reader, config);
controller.start();
let motion = controller.update().await?;
```

### 2. 异步任务
```rust
let mut task = TrackingTask::new(reader, config);
task.run(|motion| {
    control_motors(motion);
}).await?;
```

### 3. 数据记录
```rust
let mut logger = SensorDataLogger::new();
logger.log(readings);
let stats = logger.statistics();
```

### 4. 错误处理
```rust
let mut error_handler = ErrorHandler::new(3);
let strategy = error_handler.handle_error(error);
```

## 兼容性

### 硬件平台
- ✅ STM32系列
- ✅ ESP32系列  
- ✅ nRF系列
- ✅ 任何支持Embassy的平台

### 编译目标
- ✅ ARM Cortex-M
- ✅ RISC-V
- ✅ x86_64 (用于测试)

### Rust版本
- ✅ Rust 1.75+
- ✅ nightly支持

## 示例代码

库包含以下示例：
- `examples/basic_usage.rs` - 基本使用示例
- `examples/advanced_usage.rs` - 高级功能示例

## 文档

- `README.md` - 项目介绍和快速开始
- `FEATURES.md` - 功能特性详细说明
- API文档 - 通过 `cargo doc` 生成

## 测试

库支持以下测试方式：
- 单元测试（通过模拟传感器）
- 集成测试
- 硬件在环测试

## 许可证

MIT License - 开源友好，商业使用无限制
