//! # 传感器模块
//!
//! 处理五路循迹传感器的读取和状态转换

use crate::types::*;
use embedded_hal::digital::InputPin;

/// 五路循迹传感器
///
/// 直接使用 embedded-hal 的 InputPin trait 进行硬件抽象
pub struct FiveTrackSensors<T>
where
    T: InputPin,
{
    /// 最左侧传感器 (FarLeft)
    pub pin_far_left: T,
    /// 左侧传感器 (Left)
    pub pin_left: T,
    /// 中间传感器 (Center)
    pub pin_center: T,
    /// 右侧传感器 (Right)
    pub pin_right: T,
    /// 最右侧传感器 (FarRight)
    pub pin_far_right: T,
    /// 传感器反转标志
    inverted: [bool; 5],
}

impl<T> FiveTrackSensors<T>
where
    T: InputPin,
{
    /// 创建新的五路传感器实例
    pub fn new(
        pin_far_left: T,
        pin_left: T,
        pin_center: T,
        pin_right: T,
        pin_far_right: T,
    ) -> Self {
        Self {
            pin_far_left,
            pin_left,
            pin_center,
            pin_right,
            pin_far_right,
            inverted: [false; 5],
        }
    }

    /// 创建带反转配置的传感器实例
    pub fn new_with_invert(
        pin_far_left: T,
        pin_left: T,
        pin_center: T,
        pin_right: T,
        pin_far_right: T,
        inverted: [bool; 5],
    ) -> Self {
        Self {
            pin_far_left,
            pin_left,
            pin_center,
            pin_right,
            pin_far_right,
            inverted,
        }
    }

    /// 设置单个传感器的反转状态
    pub fn set_invert(&mut self, position: SensorPosition, inverted: bool) {
        self.inverted[position as usize] = inverted;
    }

    /// 读取所有传感器状态
    pub fn read(&mut self) -> Result<SensorReadings, TrackError> {
        // 读取原始引脚状态
        let pin_states = [
            self.pin_far_left
                .is_high()
                .map_err(|_| TrackError::SensorRead)?,
            self.pin_left
                .is_high()
                .map_err(|_| TrackError::SensorRead)?,
            self.pin_center
                .is_high()
                .map_err(|_| TrackError::SensorRead)?,
            self.pin_right
                .is_high()
                .map_err(|_| TrackError::SensorRead)?,
            self.pin_far_right
                .is_high()
                .map_err(|_| TrackError::SensorRead)?,
        ];

        // 转换为传感器状态，考虑反转
        let mut states = [SensorState::White; 5];
        for i in 0..5 {
            let is_black = if self.inverted[i] {
                !pin_states[i]
            } else {
                pin_states[i]
            };

            states[i] = if is_black {
                SensorState::Black
            } else {
                SensorState::White
            };
        }

        Ok(SensorReadings::new(states))
    }

    /// 读取原始引脚状态（用于调试）
    pub fn read_raw(&mut self) -> Result<[bool; 5], TrackError> {
        Ok([
            self.pin_far_left
                .is_high()
                .map_err(|_| TrackError::SensorRead)?,
            self.pin_left
                .is_high()
                .map_err(|_| TrackError::SensorRead)?,
            self.pin_center
                .is_high()
                .map_err(|_| TrackError::SensorRead)?,
            self.pin_right
                .is_high()
                .map_err(|_| TrackError::SensorRead)?,
            self.pin_far_right
                .is_high()
                .map_err(|_| TrackError::SensorRead)?,
        ])
    }
}

/// 传感器状态转换为字符串（用于调试）
pub fn states_to_string(states: &[SensorState; 5]) -> [char; 5] {
    let mut result = ['W'; 5];
    for (i, &state) in states.iter().enumerate() {
        result[i] = match state {
            SensorState::Black => 'B',
            SensorState::White => 'W',
        };
    }
    result
}

/// 传感器状态转换为数字（用于调试）
pub fn states_to_binary(states: &[SensorState; 5]) -> u8 {
    let mut result = 0u8;
    for (i, &state) in states.iter().enumerate() {
        if state == SensorState::Black {
            result |= 1 << i;
        }
    }
    result
}
