//! # 基本类型定义
//!
//! 定义五路循迹传感器库的基本数据类型

/// 传感器状态
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum SensorState {
    /// 检测到黑线
    Black,
    /// 检测到白线/背景
    White,
}

/// 传感器位置
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum SensorPosition {
    /// 最左侧传感器
    FarLeft = 0,
    /// 左侧传感器
    Left = 1,
    /// 中间传感器
    Center = 2,
    /// 右侧传感器
    Right = 3,
    /// 最右侧传感器
    FarRight = 4,
}

impl SensorPosition {
    /// 获取所有传感器位置
    pub const fn all() -> [Self; 5] {
        [
            Self::FarLeft,
            Self::Left,
            Self::Center,
            Self::Right,
            Self::FarRight,
        ]
    }

    /// 获取传感器权重（用于位置计算）
    pub const fn weight(self) -> i8 {
        match self {
            Self::FarLeft => -2,
            Self::Left => -1,
            Self::Center => 0,
            Self::Right => 1,
            Self::FarRight => 2,
        }
    }
}

/// 五路传感器读数
#[derive(Debug, <PERSON>lone, <PERSON><PERSON>)]
pub struct SensorReadings {
    /// 各传感器的状态
    pub states: [SensorState; 5],
}

impl SensorReadings {
    /// 创建新的传感器读数
    pub fn new(states: [SensorState; 5]) -> Self {
        Self { states }
    }

    /// 获取检测到黑线的传感器数量
    pub fn black_count(&self) -> usize {
        self.states
            .iter()
            .filter(|&&state| state == SensorState::Black)
            .count()
    }

    /// 计算线路位置 (-2.0 到 2.0，0为中心)
    pub fn line_position(&self) -> Option<f32> {
        let mut weighted_sum = 0i32;
        let mut total_weight = 0u32;

        for (i, &state) in self.states.iter().enumerate() {
            if state == SensorState::Black {
                let pos = SensorPosition::all()[i];
                weighted_sum += pos.weight() as i32;
                total_weight += 1;
            }
        }

        if total_weight > 0 {
            Some(weighted_sum as f32 / total_weight as f32)
        } else {
            None
        }
    }

    /// 检查是否检测到交叉路口
    pub fn is_intersection(&self) -> bool {
        self.black_count() >= 4
    }

    /// 检查是否丢失线路
    pub fn is_line_lost(&self) -> bool {
        self.black_count() == 0
    }
}

/// 循迹状态
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TrackState {
    /// 正常循迹
    Following,
    /// 线路丢失
    LineLost,
    /// 检测到交叉路口
    Intersection,
    /// 急转弯
    SharpTurn,
    /// 停止
    Stopped,
}

/// 错误类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TrackError {
    /// 传感器读取错误
    SensorRead,
    /// 硬件错误
    Hardware,
}
