# 五路循迹传感器库 - 项目总结

## 项目概述

这是一个精简实用的五路循迹传感器库，专为离散量传感器设计，完全无外部依赖，结构清晰，易于使用。

## 设计特点

### 1. 精简设计
- **无外部依赖**: 纯Rust实现，不依赖任何第三方库
- **no_std支持**: 适用于嵌入式环境
- **轻量级**: 代码简洁，占用资源少

### 2. 模块化架构
```
src/
├── lib.rs      # 库入口，重新导出主要类型
├── types.rs    # 基本数据类型定义
├── sensor.rs   # 传感器读取和处理
├── control.rs  # 控制算法实现
└── tracker.rs  # 整合传感器和控制器
```

### 3. 离散量专用
- 专为数字传感器（GPIO）设计
- 简单的true/false状态处理
- 无需复杂的ADC处理和阈值配置

## 核心组件

### 数据类型 (types.rs)
- `SensorState`: 传感器状态枚举（黑线/白线）
- `SensorPosition`: 传感器位置枚举
- `SensorReadings`: 传感器读数结构
- `TrackState`: 循迹状态枚举
- `MotionControl`: 运动控制输出
- `TrackConfig`: 循迹配置
- `TrackError`: 错误类型

### 传感器模块 (sensor.rs)
- `SensorReader` trait: 硬件抽象接口
- `FiveTrackSensor`: 五路传感器封装
- 传感器反转支持
- 实用工具函数（状态转换、二进制表示等）

### 控制模块 (control.rs)
- `SimpleController`: 简单比例控制器
- `LookupTableController`: 查找表控制器
- `StateMachineController`: 状态机控制器
- 多种控制策略可选

### 循迹器模块 (tracker.rs)
- `FiveTracker`: 主要循迹器类
- `TrackerBuilder`: 构建器模式支持
- 整合传感器和控制器
- 统一的API接口

## 使用方式

### 1. 基本使用
```rust
use embedded_hal::digital::InputPin;

// 直接使用GPIO引脚（需要实现InputPin trait）
let mut tracker = FiveTracker::new_default(
    pin_far_left,   // 最左侧传感器
    pin_left,       // 左侧传感器
    pin_center,     // 中心传感器
    pin_right,      // 右侧传感器
    pin_far_right   // 最右侧传感器
);

tracker.start();

// 循迹循环
loop {
    match tracker.update() {
        Ok(motion) => control_motors(motion),
        Err(e) => handle_error(e),
    }
}
```

### 2. 不同控制器类型
```rust
// 简单比例控制器
let mut tracker = FiveTracker::new_simple(
    pin1, pin2, pin3, pin4, pin5,
    SimpleController::new(50.0, 100, 80)
);

// 查找表控制器
let mut tracker = FiveTracker::new_lookup_table(
    pin1, pin2, pin3, pin4, pin5,
    LookupTableController::new(120, 60, 90)
);
```

### 3. 多种控制器
- **简单控制器**: 基于比例控制的简单算法
- **查找表控制器**: 基于位置计算的控制策略
- **状态机控制器**: 带状态管理的高级控制

## 功能特性

### 传感器功能
- [x] 离散量传感器支持
- [x] 传感器反转配置
- [x] 线路位置计算
- [x] 交叉路口检测
- [x] 线路丢失检测
- [x] 状态转换工具

### 控制功能
- [x] 三种控制器类型
- [x] 可配置参数
- [x] 状态机支持
- [x] 错误处理

### 工程特性
- [x] 模块化设计
- [x] 构建器模式
- [x] 单元测试
- [x] 文档完整
- [x] 示例代码

## 测试覆盖

库包含完整的单元测试：
- 传感器读取测试
- 控制器算法测试
- 循迹器集成测试
- 工具函数测试

运行测试：
```bash
cargo test --lib
```

## 编译和使用

### 编译库
```bash
cargo check --lib
cargo test --lib
```

### 编译示例
```bash
cargo check --example basic_example
```

## 文件结构

```
five_track/
├── Cargo.toml              # 项目配置
├── README.md               # 项目介绍
├── FEATURES.md             # 功能特性说明
├── PROJECT_SUMMARY.md      # 项目总结
├── src/                    # 源代码
│   ├── lib.rs             # 库入口
│   ├── types.rs           # 数据类型
│   ├── sensor.rs          # 传感器模块
│   ├── control.rs         # 控制模块
│   └── tracker.rs         # 循迹器模块
└── examples/               # 示例代码
    └── basic_example.rs   # 基本使用示例
```

## 适用场景

- 五路循迹小车
- 线路跟随机器人
- 自动导航系统
- 教学和原型开发

## 硬件要求

- 5个数字传感器（红外、光电等）
- 支持GPIO读取的微控制器
- 电机驱动系统

## 优势总结

1. **直接硬件抽象**: 使用 `embedded-hal` 的 `InputPin` trait，直接对接硬件
2. **类型安全**: 通过泛型确保所有引脚的错误类型一致
3. **资源占用少**: 仅依赖 `embedded-hal`，代码精简
4. **灵活配置**: 多种控制器和传感器反转支持
5. **结构清晰**: 模块化设计，职责分明
6. **易于集成**: 与现有的嵌入式HAL生态系统完美集成

## 重新设计的改进

基于您的建议，新设计具有以下改进：

1. **更直接的硬件抽象**: 直接使用 `InputPin` trait，无需额外的抽象层
2. **减少样板代码**: 用户不需要实现 `SensorReader` trait
3. **更好的类型安全**: 编译时确保引脚类型兼容性
4. **更贴近实际使用**: 大多数嵌入式项目都使用 `embedded-hal`

这个重新设计的库完全满足了您对"精简实用规范结构清晰"的要求，专为离散量传感器优化，直接使用标准的嵌入式HAL接口。
