# 五路循迹传感器库

精简实用的五路循迹传感器库，专为离散量传感器设计，无外部依赖。

## 特性

- 🎯 **精简设计**: 无外部依赖，纯Rust实现
- 🔧 **离散量支持**: 专为数字传感器（GPIO）设计
- 📊 **多种控制器**: 简单比例、查找表、状态机控制
- 🛠️ **灵活配置**: 支持传感器反转和参数调整
- 🏗️ **构建器模式**: 链式API，配置简单直观
- 🔄 **错误处理**: 简洁的错误处理机制
- 📝 **结构清晰**: 模块化设计，易于理解和扩展

## 快速开始

### 添加依赖

在你的 `Cargo.toml` 中添加：

```toml
[dependencies]
five_track = { path = "path/to/five_track" }
```

### 基本使用

```rust
use five_track::*;
use embedded_hal::digital::InputPin;

fn main() {
    // 假设你有5个GPIO引脚实现了InputPin trait
    let pin1 = gpio_pin1;  // 最左侧传感器
    let pin2 = gpio_pin2;  // 左侧传感器
    let pin3 = gpio_pin3;  // 中心传感器
    let pin4 = gpio_pin4;  // 右侧传感器
    let pin5 = gpio_pin5;  // 最右侧传感器

    // 创建循迹器
    let mut tracker = FiveTracker::new_default(pin1, pin2, pin3, pin4, pin5);

    // 开始循迹
    tracker.start();

    loop {
        match tracker.update() {
            Ok(motion) => {
                // 控制电机
                control_motors(motion.left_speed, motion.right_speed);
            }
            Err(e) => {
                println!("错误: {:?}", e);
                break;
            }
        }
    }
}
```

## 核心组件

### 1. 直接使用GPIO引脚

使用 `embedded-hal` 的 `InputPin` trait：

```rust
use embedded_hal::digital::InputPin;

// 你的GPIO引脚需要实现InputPin trait
let mut tracker = FiveTracker::new_default(pin1, pin2, pin3, pin4, pin5);
```

### 2. 控制器类型

#### 简单比例控制器

```rust
let controller = SimpleController::new(50.0, 100, 80);
let mut tracker = FiveTracker::new_simple(pin1, pin2, pin3, pin4, pin5, controller);
```

#### 查找表控制器

```rust
let controller = LookupTableController::new(120, 60, 90);
let mut tracker = FiveTracker::new_lookup_table(pin1, pin2, pin3, pin4, pin5, controller);
```

#### 状态机控制器

```rust
let config = TrackConfig {
    base_speed: 100,
    turn_speed: 50,
    sharp_turn_speed: 80,
};
let controller = StateMachineController::new(config);
let mut tracker = FiveTracker::new_state_machine(pin1, pin2, pin3, pin4, pin5, controller);
```

### 3. 传感器配置

```rust
// 设置传感器反转
tracker.set_sensor_invert(SensorPosition::FarLeft, true);

// 读取传感器状态
let readings = tracker.read_sensors()?;
let raw_states = tracker.read_raw_sensors()?;
```

## 高级功能

### 传感器工具函数

```rust
// 传感器状态转换为字符串
let states = [SensorState::White, SensorState::Black, SensorState::Black,
              SensorState::White, SensorState::Black];
let string_repr = states_to_string(&states); // ['W', 'B', 'B', 'W', 'B']

// 传感器状态转换为二进制
let binary_repr = states_to_binary(&states); // 0b11010
```

### 传感器读数分析

```rust
let readings = SensorReadings::new(states);

// 基本信息
println!("黑线数量: {}", readings.black_count());
println!("是否交叉路口: {}", readings.is_intersection());
println!("是否线路丢失: {}", readings.is_line_lost());

// 线路位置计算
if let Some(position) = readings.line_position() {
    println!("线路位置: {:.2} (-2.0到2.0)", position);
}
```

### 错误处理

```rust
match tracker.update() {
    Ok(motion) => {
        // 正常处理运动控制
        control_motors(motion);
    }
    Err(TrackError::SensorRead) => {
        // 传感器读取错误
        println!("传感器读取失败");
    }
    Err(TrackError::Hardware) => {
        // 硬件错误
        println!("硬件故障");
    }
}
```

## 示例

查看 `examples/` 目录中的完整示例：

- `simple_usage.rs` - 基本使用示例
- `advanced_usage.rs` - 高级功能和多种控制器示例

运行示例：

```bash
cargo run --example simple_usage
cargo run --example advanced_usage
```

## API 文档

生成完整的API文档：

```bash
cargo doc --open
```

## 支持的硬件

这个库设计为硬件无关，你需要实现 `SensorReader` trait 来适配具体硬件：

- STM32系列微控制器
- ESP32系列
- nRF系列
- 任何支持Embassy的平台

## 贡献

欢迎贡献代码！请确保：

1. 代码遵循Rust标准格式
2. 添加适当的测试
3. 更新文档
4. 遵循现有的代码风格

## 许可证

MIT License - 详见 LICENSE 文件

## 更新日志

### v0.1.0

- 初始版本
- 支持离散量传感器
- 三种控制器类型（简单比例、查找表、状态机）
- 构建器模式API
- 传感器反转支持
- 基本错误处理
- 工具函数集合
- 无外部依赖设计
