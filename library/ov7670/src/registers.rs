//! OV7670 camera sensor register definitions and constants
//!
//! This module contains all the register addresses and configuration values
//! for the OV7670 camera sensor.

/// OV7670 I2C slave address (7-bit)
pub const OV7670_I2C_ADDR: u8 = 0x21;

/// Register addresses
pub mod reg {
    pub const GAIN: u8 = 0x00;
    pub const BLUE: u8 = 0x01;
    pub const RED: u8 = 0x02;
    pub const VREF: u8 = 0x03;
    pub const COM1: u8 = 0x04;
    pub const BAVE: u8 = 0x05;
    pub const GB_AVE: u8 = 0x06;
    pub const AECHH: u8 = 0x07;
    pub const RAVE: u8 = 0x08;
    pub const COM2: u8 = 0x09;
    pub const PID: u8 = 0x0A;
    pub const VER: u8 = 0x0B;
    pub const COM3: u8 = 0x0C;
    pub const COM4: u8 = 0x0D;
    pub const COM5: u8 = 0x0E;
    pub const COM6: u8 = 0x0F;
    pub const AECH: u8 = 0x10;
    pub const CLKRC: u8 = 0x11;
    pub const COM7: u8 = 0x12;
    pub const COM8: u8 = 0x13;
    pub const COM9: u8 = 0x14;
    pub const COM10: u8 = 0x15;
    pub const HSTART: u8 = 0x17;
    pub const HSTOP: u8 = 0x18;
    pub const VSTART: u8 = 0x19;
    pub const VSTOP: u8 = 0x1A;
    pub const PSHFT: u8 = 0x1B;
    pub const MIDH: u8 = 0x1C;
    pub const MIDL: u8 = 0x1D;
    pub const MVFP: u8 = 0x1E;
    pub const LAEC: u8 = 0x1F;
    pub const ADCCTR0: u8 = 0x20;
    pub const ADCCTR1: u8 = 0x21;
    pub const ADCCTR2: u8 = 0x22;
    pub const ADCCTR3: u8 = 0x23;
    pub const AEW: u8 = 0x24;
    pub const AEB: u8 = 0x25;
    pub const VPT: u8 = 0x26;
    pub const BBIAS: u8 = 0x27;
    pub const GB_BIAS: u8 = 0x28;
    pub const EXHCH: u8 = 0x2A;
    pub const EXHCL: u8 = 0x2B;
    pub const RBIAS: u8 = 0x2C;
    pub const ADVFL: u8 = 0x2D;
    pub const ADVFH: u8 = 0x2E;
    pub const YAVE: u8 = 0x2F;
    pub const HSYST: u8 = 0x30;
    pub const HSYEN: u8 = 0x31;
    pub const HREF: u8 = 0x32;
    pub const CHLF: u8 = 0x33;
    pub const ARBLM: u8 = 0x34;
    pub const ADC: u8 = 0x37;
    pub const ACOM: u8 = 0x38;
    pub const OFON: u8 = 0x39;
    pub const TSLB: u8 = 0x3A;
    pub const COM11: u8 = 0x3B;
    pub const COM12: u8 = 0x3C;
    pub const COM13: u8 = 0x3D;
    pub const COM14: u8 = 0x3E;
    pub const EDGE: u8 = 0x3F;
    pub const COM15: u8 = 0x40;
    pub const COM16: u8 = 0x41;
    pub const COM17: u8 = 0x42;
    pub const AWBC1: u8 = 0x43;
    pub const AWBC2: u8 = 0x44;
    pub const AWBC3: u8 = 0x45;
    pub const AWBC4: u8 = 0x46;
    pub const AWBC5: u8 = 0x47;
    pub const AWBC6: u8 = 0x48;
    pub const REG4B: u8 = 0x4B;
    pub const DNSTH: u8 = 0x4C;
    pub const MTX1: u8 = 0x4F;
    pub const MTX2: u8 = 0x50;
    pub const MTX3: u8 = 0x51;
    pub const MTX4: u8 = 0x52;
    pub const MTX5: u8 = 0x53;
    pub const MTX6: u8 = 0x54;
    pub const BRIGHT: u8 = 0x55;
    pub const CONTRAS: u8 = 0x56;
    pub const CONTRAS_CENTER: u8 = 0x57;
    pub const MTXS: u8 = 0x58;
    pub const LCC1: u8 = 0x62;
    pub const LCC2: u8 = 0x63;
    pub const LCC3: u8 = 0x64;
    pub const LCC4: u8 = 0x65;
    pub const LCC5: u8 = 0x66;
    pub const MANU: u8 = 0x67;
    pub const MANV: u8 = 0x68;
    pub const GFIX: u8 = 0x69;
    pub const GGAIN: u8 = 0x6A;
    pub const DBLV: u8 = 0x6B;
    pub const AWBCTR3: u8 = 0x6C;
    pub const AWBCTR2: u8 = 0x6D;
    pub const AWBCTR1: u8 = 0x6E;
    pub const AWBCTR0: u8 = 0x6F;
    pub const SCALING_XSC: u8 = 0x70;
    pub const SCALING_YSC: u8 = 0x71;
    pub const SCALING_DCWCTR: u8 = 0x72;
    pub const SCALING_PCLK_DIV: u8 = 0x73;
    pub const REG74: u8 = 0x74;
    pub const REG75: u8 = 0x75;
    pub const REG76: u8 = 0x76;
    pub const REG77: u8 = 0x77;
    pub const SLOP: u8 = 0x7A;
    pub const GAM1: u8 = 0x7B;
    pub const GAM2: u8 = 0x7C;
    pub const GAM3: u8 = 0x7D;
    pub const GAM4: u8 = 0x7E;
    pub const GAM5: u8 = 0x7F;
    pub const GAM6: u8 = 0x80;
    pub const GAM7: u8 = 0x81;
    pub const GAM8: u8 = 0x82;
    pub const GAM9: u8 = 0x83;
    pub const GAM10: u8 = 0x84;
    pub const GAM11: u8 = 0x85;
    pub const GAM12: u8 = 0x86;
    pub const GAM13: u8 = 0x87;
    pub const GAM14: u8 = 0x88;
    pub const GAM15: u8 = 0x89;
    pub const RGB444: u8 = 0x8C;
    pub const DM_LNL: u8 = 0x92;
    pub const DM_LNH: u8 = 0x93;
    pub const LCC6: u8 = 0x94;
    pub const LCC7: u8 = 0x95;
    pub const BD50ST: u8 = 0x9D;
    pub const BD60ST: u8 = 0x9E;
    pub const HAECC1: u8 = 0x9F;
    pub const HAECC2: u8 = 0xA0;
    pub const SCALING_PCLK_DELAY: u8 = 0xA2;
    pub const NT_CTRL: u8 = 0xA4;
    pub const BD50MAX: u8 = 0xA5;
    pub const HAECC3: u8 = 0xA6;
    pub const HAECC4: u8 = 0xA7;
    pub const HAECC5: u8 = 0xA8;
    pub const HAECC6: u8 = 0xA9;
    pub const HAECC7: u8 = 0xAA;
    pub const BD60MAX: u8 = 0xAB;
    pub const STR_OPT: u8 = 0xAC;
    pub const STR_R: u8 = 0xAD;
    pub const STR_G: u8 = 0xAE;
    pub const STR_B: u8 = 0xAF;
    pub const ABLC1: u8 = 0xB1;
    pub const THL_ST: u8 = 0xB3;
    pub const THL_DLT: u8 = 0xB5;
    pub const AD_CHB: u8 = 0xBE;
    pub const AD_CHR: u8 = 0xBF;
    pub const AD_CHGB: u8 = 0xC0;
    pub const AD_CHGR: u8 = 0xC1;
    pub const SATCTR: u8 = 0xC9;
}
/// Product ID and Version values
pub mod id {
    pub const PID_VALUE: u8 = 0x76;
    pub const VER_VALUE: u8 = 0x73;
}

/// COM7 register bit definitions (Output format control)
pub mod com7 {
    pub const RESET: u8 = 0x80;
    pub const FMT_MASK: u8 = 0x38;
    pub const FMT_VGA: u8 = 0x00;
    pub const FMT_CIF: u8 = 0x20;
    pub const FMT_QVGA: u8 = 0x10;
    pub const FMT_QCIF: u8 = 0x08;
    pub const RGB: u8 = 0x04;
    pub const YUV: u8 = 0x00;
    pub const BAYER: u8 = 0x01;
    pub const PBAYER: u8 = 0x05;
}

/// COM15 register bit definitions (Output format control)
pub mod com15 {
    pub const R10F0: u8 = 0x00;
    pub const R01FE: u8 = 0x80;
    pub const R00FF: u8 = 0xC0;
    pub const RGB565: u8 = 0x10;
    pub const RGB555: u8 = 0x30;
}

/// TSLB register bit definitions
pub mod tslb {
    pub const YLAST: u8 = 0x04;
    pub const UV: u8 = 0x10;
}

/// MVFP register bit definitions (Mirror/VFlip)
pub mod mvfp {
    pub const MIRROR: u8 = 0x20;
    pub const FLIP: u8 = 0x10;
}

/// Clock divider values for CLKRC register
pub mod clkrc {
    pub const CLK_EXT: u8 = 0x40;
    pub const CLK_SCALE: u8 = 0x3F;
}

/// COM8 register bit definitions (AGC/AEC)
pub mod com8 {
    pub const FAST_AUTO: u8 = 0x80;
    pub const STEP_VSYNC: u8 = 0x00;
    pub const STEP_UNLIMITED: u8 = 0x01;
    pub const DROP_VSYNC: u8 = 0x00;
    pub const DROP_FRAME: u8 = 0x02;
    pub const AEC_EN: u8 = 0x01;
    pub const AWB_EN: u8 = 0x02;
    pub const AGC_EN: u8 = 0x04;
    pub const BFILT: u8 = 0x20;
}

/// COM3 register bit definitions
pub mod com3 {
    pub const SWAP: u8 = 0x40;
    pub const SCALEEN: u8 = 0x08;
    pub const DCWEN: u8 = 0x04;
}

/// COM14 register bit definitions
pub mod com14 {
    pub const DCWEN: u8 = 0x80;
}
