//! Image processing utilities for OV7670 captured data
//!
//! This module provides utilities for processing image data captured from
//! the OV7670 camera sensor, including format conversion, cropping, and scaling.

use defmt::Format;

/// Image processing error types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum ImageError {
    /// Invalid image dimensions
    InvalidDimensions,
    /// Unsupported format conversion
    UnsupportedConversion,
    /// Buffer too small for operation
    BufferTooSmall,
    /// Invalid parameters
    InvalidParameters,
}

/// Result type for image processing operations
pub type ImageResult<T> = core::result::Result<T, ImageError>;

/// RGB565 pixel representation
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub struct Rgb565(pub u16);

impl Rgb565 {
    /// Create RGB565 from individual color components
    pub fn from_rgb(r: u8, g: u8, b: u8) -> Self {
        let r = (r >> 3) as u16;
        let g = (g >> 2) as u16;
        let b = (b >> 3) as u16;
        Self((r << 11) | (g << 5) | b)
    }

    /// Extract RGB components
    pub fn to_rgb(self) -> (u8, u8, u8) {
        let r = ((self.0 >> 11) & 0x1F) as u8;
        let g = ((self.0 >> 5) & 0x3F) as u8;
        let b = (self.0 & 0x1F) as u8;

        // Scale to 8-bit
        (r << 3, g << 2, b << 3)
    }

    /// Get raw RGB565 value
    pub fn raw(self) -> u16 {
        self.0
    }
}

/// YUV422 pixel pair representation
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub struct Yuv422 {
    pub y1: u8,
    pub u: u8,
    pub y2: u8,
    pub v: u8,
}

impl Yuv422 {
    /// Create YUV422 from bytes
    pub fn from_bytes(bytes: [u8; 4]) -> Self {
        Self {
            y1: bytes[0],
            u: bytes[1],
            y2: bytes[2],
            v: bytes[3],
        }
    }

    /// Convert to bytes
    pub fn to_bytes(self) -> [u8; 4] {
        [self.y1, self.u, self.y2, self.v]
    }

    /// Convert YUV422 pixel pair to two RGB565 pixels
    pub fn to_rgb565_pair(self) -> (Rgb565, Rgb565) {
        let (r1, g1, b1) = yuv_to_rgb(self.y1, self.u, self.v);
        let (r2, g2, b2) = yuv_to_rgb(self.y2, self.u, self.v);

        (Rgb565::from_rgb(r1, g1, b1), Rgb565::from_rgb(r2, g2, b2))
    }
}

/// YUV to RGB conversion constants (fixed-point arithmetic)
const YUV_R_COEFF: i32 = 1436; // 1.402 * 1024
const YUV_G_U_COEFF: i32 = 352; // 0.344 * 1024
const YUV_G_V_COEFF: i32 = 731; // 0.714 * 1024
const YUV_B_COEFF: i32 = 1815; // 1.772 * 1024
const YUV_SHIFT: i32 = 10; // 2^10 = 1024

/// Convert YUV to RGB using fixed-point arithmetic (no floating point)
fn yuv_to_rgb(y: u8, u: u8, v: u8) -> (u8, u8, u8) {
    let y = i32::from(y);
    let u = i32::from(u) - 128;
    let v = i32::from(v) - 128;

    let r = y + ((YUV_R_COEFF * v) >> YUV_SHIFT);
    let g = y - ((YUV_G_U_COEFF * u) >> YUV_SHIFT) - ((YUV_G_V_COEFF * v) >> YUV_SHIFT);
    let b = y + ((YUV_B_COEFF * u) >> YUV_SHIFT);

    (
        r.clamp(0, 255) as u8,
        g.clamp(0, 255) as u8,
        b.clamp(0, 255) as u8,
    )
}

/// Image format conversion utilities
pub struct FormatConverter;

impl FormatConverter {
    /// Convert YUV422 buffer to RGB565 buffer
    pub fn yuv422_to_rgb565(
        yuv_buffer: &[u8],
        rgb_buffer: &mut [u8],
        width: u16,
        height: u16,
    ) -> ImageResult<()> {
        let pixel_count = usize::from(width) * usize::from(height);
        let expected_yuv_size = pixel_count * 2;
        let expected_rgb_size = pixel_count * 2;

        if yuv_buffer.len() < expected_yuv_size {
            return Err(ImageError::BufferTooSmall);
        }

        if rgb_buffer.len() < expected_rgb_size {
            return Err(ImageError::BufferTooSmall);
        }

        // Process YUV422 data in chunks of 4 bytes (2 pixels)
        for (chunk_idx, yuv_chunk) in yuv_buffer.chunks_exact(4).enumerate() {
            let yuv = Yuv422::from_bytes([yuv_chunk[0], yuv_chunk[1], yuv_chunk[2], yuv_chunk[3]]);

            let (rgb1, rgb2) = yuv.to_rgb565_pair();

            let rgb_base_idx = chunk_idx * 4;
            rgb_buffer[rgb_base_idx] = (rgb1.raw() & 0xFF) as u8;
            rgb_buffer[rgb_base_idx + 1] = (rgb1.raw() >> 8) as u8;
            rgb_buffer[rgb_base_idx + 2] = (rgb2.raw() & 0xFF) as u8;
            rgb_buffer[rgb_base_idx + 3] = (rgb2.raw() >> 8) as u8;
        }

        Ok(())
    }

    /// Convert RGB565 buffer to RGB888 buffer
    pub fn rgb565_to_rgb888(
        rgb565_buffer: &[u8],
        rgb888_buffer: &mut [u8],
        width: u16,
        height: u16,
    ) -> ImageResult<()> {
        let pixel_count = usize::from(width) * usize::from(height);
        let expected_565_size = pixel_count * 2;
        let expected_888_size = pixel_count * 3;

        if rgb565_buffer.len() < expected_565_size {
            return Err(ImageError::BufferTooSmall);
        }

        if rgb888_buffer.len() < expected_888_size {
            return Err(ImageError::BufferTooSmall);
        }

        // Process RGB565 data in chunks of 2 bytes (1 pixel)
        for (pixel_idx, rgb565_chunk) in rgb565_buffer.chunks_exact(2).enumerate() {
            let rgb565_val = u16::from(rgb565_chunk[0]) | (u16::from(rgb565_chunk[1]) << 8);
            let rgb565 = Rgb565(rgb565_val);
            let (r, g, b) = rgb565.to_rgb();

            let rgb888_base_idx = pixel_idx * 3;
            rgb888_buffer[rgb888_base_idx] = r;
            rgb888_buffer[rgb888_base_idx + 1] = g;
            rgb888_buffer[rgb888_base_idx + 2] = b;
        }

        Ok(())
    }
}
/// Parameters for image cropping operation
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub struct CropParams {
    /// Source image width
    pub src_width: u16,
    /// Source image height
    pub src_height: u16,
    /// Crop region X coordinate
    pub crop_x: u16,
    /// Crop region Y coordinate
    pub crop_y: u16,
    /// Crop region width
    pub crop_width: u16,
    /// Crop region height
    pub crop_height: u16,
    /// Bytes per pixel
    pub bytes_per_pixel: u8,
}

/// Parameters for image scaling operation
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub struct ScaleParams {
    /// Source image width
    pub src_width: u16,
    /// Source image height
    pub src_height: u16,
    /// Target image width
    pub dst_width: u16,
    /// Target image height
    pub dst_height: u16,
    /// Bytes per pixel
    pub bytes_per_pixel: u8,
}

/// Image manipulation utilities
pub struct ImageProcessor;

impl ImageProcessor {
    /// Crop an image region
    pub fn crop(src_buffer: &[u8], dst_buffer: &mut [u8], params: &CropParams) -> ImageResult<()> {
        // Validate crop region
        if params.crop_x + params.crop_width > params.src_width
            || params.crop_y + params.crop_height > params.src_height
        {
            return Err(ImageError::InvalidParameters);
        }

        let src_stride = usize::from(params.src_width) * usize::from(params.bytes_per_pixel);
        let dst_stride = usize::from(params.crop_width) * usize::from(params.bytes_per_pixel);
        let expected_dst_size = usize::from(params.crop_height) * dst_stride;

        if dst_buffer.len() < expected_dst_size {
            return Err(ImageError::BufferTooSmall);
        }

        for y in 0..params.crop_height {
            let src_y = params.crop_y + y;
            let src_offset = usize::from(src_y) * src_stride
                + usize::from(params.crop_x) * usize::from(params.bytes_per_pixel);
            let dst_offset = usize::from(y) * dst_stride;

            dst_buffer[dst_offset..dst_offset + dst_stride]
                .copy_from_slice(&src_buffer[src_offset..src_offset + dst_stride]);
        }

        Ok(())
    }

    /// Simple nearest-neighbor scaling
    pub fn scale_nearest(
        src_buffer: &[u8],
        dst_buffer: &mut [u8],
        params: &ScaleParams,
    ) -> ImageResult<()> {
        // Validate input parameters
        if params.src_width == 0
            || params.src_height == 0
            || params.dst_width == 0
            || params.dst_height == 0
            || params.bytes_per_pixel == 0
        {
            return Err(ImageError::InvalidParameters);
        }

        let src_stride = usize::from(params.src_width) * usize::from(params.bytes_per_pixel);
        let dst_stride = usize::from(params.dst_width) * usize::from(params.bytes_per_pixel);
        let expected_src_size = usize::from(params.src_height) * src_stride;
        let expected_dst_size = usize::from(params.dst_height) * dst_stride;

        if src_buffer.len() < expected_src_size {
            return Err(ImageError::BufferTooSmall);
        }

        if dst_buffer.len() < expected_dst_size {
            return Err(ImageError::BufferTooSmall);
        }

        let x_ratio = (u32::from(params.src_width) << 16) / u32::from(params.dst_width);
        let y_ratio = (u32::from(params.src_height) << 16) / u32::from(params.dst_height);

        for dst_y in 0..params.dst_height {
            let src_y = ((u32::from(dst_y) * y_ratio) >> 16) as u16;

            for dst_x in 0..params.dst_width {
                let src_x = ((u32::from(dst_x) * x_ratio) >> 16) as u16;

                let src_offset = usize::from(src_y) * src_stride
                    + usize::from(src_x) * usize::from(params.bytes_per_pixel);
                let dst_offset = usize::from(dst_y) * dst_stride
                    + usize::from(dst_x) * usize::from(params.bytes_per_pixel);

                // Copy pixel data
                let pixel_size = usize::from(params.bytes_per_pixel);
                dst_buffer[dst_offset..dst_offset + pixel_size]
                    .copy_from_slice(&src_buffer[src_offset..src_offset + pixel_size]);
            }
        }

        Ok(())
    }

    /// Apply horizontal mirror effect
    pub fn mirror_horizontal(
        buffer: &mut [u8],
        width: u16,
        height: u16,
        bytes_per_pixel: u8,
    ) -> ImageResult<()> {
        // Validate parameters
        if width == 0 || height == 0 || bytes_per_pixel == 0 {
            return Err(ImageError::InvalidParameters);
        }

        let stride = usize::from(width) * usize::from(bytes_per_pixel);
        let pixel_size = usize::from(bytes_per_pixel);
        let expected_size = usize::from(height) * stride;

        if buffer.len() < expected_size {
            return Err(ImageError::BufferTooSmall);
        }

        for y in 0..height {
            let row_offset = usize::from(y) * stride;

            for x in 0..(width / 2) {
                let left_offset = row_offset + usize::from(x) * pixel_size;
                let right_offset = row_offset + usize::from(width - 1 - x) * pixel_size;

                // Swap pixels
                for byte_idx in 0..pixel_size {
                    buffer.swap(left_offset + byte_idx, right_offset + byte_idx);
                }
            }
        }

        Ok(())
    }

    /// Apply vertical flip effect
    pub fn flip_vertical(
        buffer: &mut [u8],
        width: u16,
        height: u16,
        bytes_per_pixel: u8,
    ) -> ImageResult<()> {
        // Validate parameters
        if width == 0 || height == 0 || bytes_per_pixel == 0 {
            return Err(ImageError::InvalidParameters);
        }

        let stride = usize::from(width) * usize::from(bytes_per_pixel);
        let expected_size = usize::from(height) * stride;

        if buffer.len() < expected_size {
            return Err(ImageError::BufferTooSmall);
        }

        for y in 0..(height / 2) {
            let top_offset = usize::from(y) * stride;
            let bottom_offset = usize::from(height - 1 - y) * stride;

            // Swap rows efficiently
            for x in 0..stride {
                buffer.swap(top_offset + x, bottom_offset + x);
            }
        }

        Ok(())
    }

    /// Simple brightness adjustment
    pub fn adjust_brightness(
        buffer: &mut [u8],
        width: u16,
        height: u16,
        bytes_per_pixel: u8,
        brightness: i16, // -255 to +255
    ) -> ImageResult<()> {
        // Validate parameters
        if width == 0 || height == 0 || bytes_per_pixel == 0 {
            return Err(ImageError::InvalidParameters);
        }

        if !(-255..=255).contains(&brightness) {
            return Err(ImageError::InvalidParameters);
        }

        let total_bytes = usize::from(width) * usize::from(height) * usize::from(bytes_per_pixel);

        if buffer.len() < total_bytes {
            return Err(ImageError::BufferTooSmall);
        }

        // Apply brightness adjustment to each byte
        for pixel_byte in buffer.iter_mut().take(total_bytes) {
            let pixel = i16::from(*pixel_byte);
            let adjusted = (pixel + brightness).clamp(0, 255);
            *pixel_byte = adjusted as u8;
        }

        Ok(())
    }

    /// Simple contrast adjustment using fixed-point arithmetic
    pub fn adjust_contrast(
        buffer: &mut [u8],
        width: u16,
        height: u16,
        bytes_per_pixel: u8,
        contrast_factor: u16, // 0 to 512, where 256 is no change (1.0x)
    ) -> ImageResult<()> {
        // Validate parameters
        if width == 0 || height == 0 || bytes_per_pixel == 0 {
            return Err(ImageError::InvalidParameters);
        }

        if contrast_factor > 512 {
            return Err(ImageError::InvalidParameters);
        }

        let total_bytes = usize::from(width) * usize::from(height) * usize::from(bytes_per_pixel);

        if buffer.len() < total_bytes {
            return Err(ImageError::BufferTooSmall);
        }

        // Apply contrast adjustment using fixed-point arithmetic
        for pixel_byte in buffer.iter_mut().take(total_bytes) {
            let pixel = i32::from(*pixel_byte);
            // Subtract 128 (middle gray), apply contrast, add 128 back
            let adjusted = (((pixel - 128) * i32::from(contrast_factor)) >> 8) + 128;
            *pixel_byte = adjusted.clamp(0, 255) as u8;
        }

        Ok(())
    }
}
