//! Image capture interfaces and utilities for OV7670
//!
//! This module provides the necessary interfaces and utilities for capturing
//! image data from the OV7670 camera sensor, including frame synchronization
//! and pixel data reading.

use defmt::Format;
use embassy_futures::select::{Either, select};
use embassy_time::{Duration, Instant, Timer};
use embedded_hal::digital::InputPin;
use embedded_hal_async::digital::Wait;

/// Capture-specific error types
#[derive(Debug, <PERSON>lone, Copy, Format)]
pub enum CaptureError {
    /// Hardware error (pin operation failed)
    HardwareError,
    /// Timeout occurred during capture
    Timeout,
    /// Buffer too small for capture
    BufferTooSmall,
    /// Frame synchronization error
    SyncError,
}

/// Result type for capture operations
pub type CaptureResult<T> = core::result::Result<T, CaptureError>;

/// Camera capture interface traits
pub trait CaptureInterface {
    type Error;

    /// VSYNC pin for frame synchronization
    type VsyncPin: InputPin + Wait;

    /// HREF pin for line synchronization
    type HrefPin: InputPin + Wait;

    /// PCLK pin for pixel clock
    type PclkPin: InputPin + Wait;

    /// Data pins for pixel data (8-bit parallel interface)
    type DataPins: PixelDataReader<Error = Self::Error>;
}

/// Trait for reading pixel data from parallel data pins
pub trait PixelDataReader {
    type Error;

    /// Read 8-bit pixel data from the data pins
    fn read_pixel_data(&mut self) -> Result<u8, Self::Error>;
}

/// Frame capture statistics
#[derive(Debug, Clone, Copy, Format, Default)]
pub struct CaptureStats {
    /// Total frames captured
    pub frames_captured: u32,
    /// Total lines captured in current frame
    pub lines_captured: u32,
    /// Total pixels captured in current line
    pub pixels_captured: u32,
    /// Frame capture start time
    pub frame_start_time: Option<Instant>,
    /// Frame capture duration
    pub frame_duration: Option<Duration>,
    /// Capture errors encountered
    pub capture_errors: u32,
}

/// Frame buffer for storing captured image data
#[derive(Debug)]
pub struct FrameBuffer<'a> {
    /// Buffer data
    pub data: &'a mut [u8],
    /// Frame width in pixels
    pub width: u16,
    /// Frame height in pixels
    pub height: u16,
    /// Bytes per pixel
    pub bytes_per_pixel: u8,
    /// Capture timestamp
    pub timestamp: Instant,
    /// Frame sequence number
    pub sequence: u32,
    /// Current write position in buffer
    pub position: usize,
}

impl<'a> FrameBuffer<'a> {
    /// Create a new frame buffer with the provided buffer
    pub fn new(buffer: &'a mut [u8], width: u16, height: u16, bytes_per_pixel: u8) -> Self {
        Self {
            data: buffer,
            width,
            height,
            bytes_per_pixel,
            timestamp: Instant::now(),
            sequence: 0,
            position: 0,
        }
    }

    /// Get the total size of the frame buffer in bytes
    pub fn size(&self) -> usize {
        self.data.len()
    }

    /// Get the expected frame size in bytes
    pub fn expected_size(&self) -> usize {
        self.width as usize * self.height as usize * self.bytes_per_pixel as usize
    }

    /// Check if the buffer is full
    pub fn is_full(&self) -> bool {
        self.position >= self.expected_size()
    }

    /// Clear the frame buffer
    pub fn clear(&mut self) {
        self.data.fill(0);
        self.timestamp = Instant::now();
        self.position = 0;
    }

    /// Write pixel data to the buffer
    pub fn write_pixel(&mut self, pixel_data: u8) -> CaptureResult<()> {
        if self.position >= self.data.len() {
            return Err(CaptureError::BufferTooSmall);
        }

        self.data[self.position] = pixel_data;
        self.position += 1;
        Ok(())
    }

    /// Get remaining space in buffer
    pub fn remaining(&self) -> usize {
        self.data.len().saturating_sub(self.position)
    }

    /// Get pixel data at specific coordinates
    pub fn get_pixel(&self, x: u16, y: u16) -> Option<&[u8]> {
        if x >= self.width || y >= self.height {
            return None;
        }

        let offset =
            (y as usize * self.width as usize + x as usize) * self.bytes_per_pixel as usize;
        let end = offset + self.bytes_per_pixel as usize;

        if end <= self.data.len() {
            Some(&self.data[offset..end])
        } else {
            None
        }
    }

    /// Set pixel data at specific coordinates
    pub fn set_pixel(&mut self, x: u16, y: u16, pixel_data: &[u8]) -> CaptureResult<()> {
        if x >= self.width || y >= self.height || pixel_data.len() != self.bytes_per_pixel as usize
        {
            return Err(CaptureError::BufferTooSmall);
        }

        let offset =
            (y as usize * self.width as usize + x as usize) * self.bytes_per_pixel as usize;
        let end = offset + self.bytes_per_pixel as usize;

        if end <= self.data.len() {
            self.data[offset..end].copy_from_slice(pixel_data);
            Ok(())
        } else {
            Err(CaptureError::BufferTooSmall)
        }
    }
}

/// Capture mode configuration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum CaptureMode {
    /// Single frame capture
    SingleFrame,
    /// Continuous frame capture
    Continuous,
    /// Capture with frame skip (capture every N frames)
    FrameSkip(u8),
}

/// Frame synchronization utilities
pub struct FrameSync<VSYNC, HREF, PCLK, DATA>
where
    VSYNC: InputPin + Wait,
    HREF: InputPin + Wait,
    PCLK: InputPin + Wait,
    DATA: PixelDataReader,
{
    vsync_pin: VSYNC,
    href_pin: HREF,
    pclk_pin: PCLK,
    data_pins: DATA,
    stats: CaptureStats,
}

impl<VSYNC, HREF, PCLK, DATA> FrameSync<VSYNC, HREF, PCLK, DATA>
where
    VSYNC: InputPin + Wait,
    HREF: InputPin + Wait,
    PCLK: InputPin + Wait,
    DATA: PixelDataReader,
{
    /// Create a new frame synchronization instance
    pub fn new(vsync_pin: VSYNC, href_pin: HREF, pclk_pin: PCLK, data_pins: DATA) -> Self {
        Self {
            vsync_pin,
            href_pin,
            pclk_pin,
            data_pins,
            stats: CaptureStats::default(),
        }
    }

    /// Wait for the start of a new frame (VSYNC rising edge)
    pub async fn wait_frame_start(&mut self) -> CaptureResult<()> {
        // Wait for VSYNC to go low first (if it's currently high)
        if self
            .vsync_pin
            .is_high()
            .map_err(|_| CaptureError::HardwareError)?
        {
            self.vsync_pin
                .wait_for_low()
                .await
                .map_err(|_| CaptureError::HardwareError)?;
        }

        // Wait for VSYNC rising edge (start of frame)
        self.vsync_pin
            .wait_for_high()
            .await
            .map_err(|_| CaptureError::HardwareError)?;

        self.stats.frame_start_time = Some(Instant::now());
        self.stats.lines_captured = 0;

        Ok(())
    }

    /// Wait for the end of a frame (VSYNC falling edge)
    pub async fn wait_frame_end(&mut self) -> CaptureResult<()> {
        // Wait for VSYNC to go low (end of frame)
        self.vsync_pin
            .wait_for_low()
            .await
            .map_err(|_| CaptureError::HardwareError)?;

        if let Some(start_time) = self.stats.frame_start_time {
            self.stats.frame_duration = Some(Instant::now() - start_time);
        }
        self.stats.frames_captured += 1;

        Ok(())
    }

    /// Wait for the start of a line (HREF rising edge)
    pub async fn wait_line_start(&mut self) -> CaptureResult<()> {
        // Wait for HREF to go low first (if it's currently high)
        if self
            .href_pin
            .is_high()
            .map_err(|_| CaptureError::HardwareError)?
        {
            self.href_pin
                .wait_for_low()
                .await
                .map_err(|_| CaptureError::HardwareError)?;
        }

        // Wait for HREF rising edge (start of line)
        self.href_pin
            .wait_for_high()
            .await
            .map_err(|_| CaptureError::HardwareError)?;

        self.stats.pixels_captured = 0;

        Ok(())
    }

    /// Wait for the end of a line (HREF falling edge)
    pub async fn wait_line_end(&mut self) -> CaptureResult<()> {
        // Wait for HREF to go low (end of line)
        self.href_pin
            .wait_for_low()
            .await
            .map_err(|_| CaptureError::HardwareError)?;

        self.stats.lines_captured += 1;

        Ok(())
    }

    /// Capture a single pixel on PCLK rising edge
    pub async fn capture_pixel(&mut self) -> CaptureResult<u8> {
        // Wait for PCLK rising edge
        self.pclk_pin
            .wait_for_high()
            .await
            .map_err(|_| CaptureError::HardwareError)?;

        // Read pixel data
        let pixel_data = self
            .data_pins
            .read_pixel_data()
            .map_err(|_| CaptureError::HardwareError)?;

        self.stats.pixels_captured += 1;

        Ok(pixel_data)
    }

    /// Capture a complete line of pixels
    pub async fn capture_line(&mut self, buffer: &mut [u8], width: u16) -> CaptureResult<()> {
        self.wait_line_start().await?;

        for i in 0..width as usize {
            if i >= buffer.len() {
                break;
            }

            buffer[i] = self.capture_pixel().await?;
        }

        self.wait_line_end().await?;
        Ok(())
    }

    /// Capture a complete frame
    pub async fn capture_frame(
        &mut self,
        frame_buffer: &mut FrameBuffer<'_>,
        timeout_ms: u32,
    ) -> CaptureResult<()> {
        let timeout = Duration::from_millis(timeout_ms as u64);

        // Wait for frame start with timeout
        match select(self.wait_frame_start(), Timer::after(timeout)).await {
            Either::First(result) => result?,
            Either::Second(_) => return Err(CaptureError::Timeout),
        }

        frame_buffer.clear();
        frame_buffer.timestamp = Instant::now();

        // Capture all lines in the frame
        for _y in 0..frame_buffer.height {
            // Wait for line start
            self.wait_line_start().await?;

            // Capture all pixels in the line
            for _x in 0..frame_buffer.width {
                let pixel = self.capture_pixel().await?;

                if frame_buffer.write_pixel(pixel).is_err() {
                    self.stats.capture_errors += 1;
                    return Err(CaptureError::BufferTooSmall);
                }
            }

            // Wait for line end
            self.wait_line_end().await?;
        }

        // Wait for frame end
        self.wait_frame_end().await?;

        frame_buffer.sequence = self.stats.frames_captured;

        Ok(())
    }

    /// Get capture statistics
    pub fn get_stats(&self) -> CaptureStats {
        self.stats
    }

    /// Reset capture statistics
    pub fn reset_stats(&mut self) {
        self.stats = CaptureStats::default();
    }
}

/// Capture configuration
#[derive(Debug, Clone, Copy, Format)]
pub struct CaptureConfig {
    /// Capture mode
    pub mode: CaptureMode,
    /// Timeout for frame capture (in milliseconds)
    pub timeout_ms: u32,
    /// Enable capture statistics
    pub enable_stats: bool,
    /// Maximum number of frames to capture (0 = unlimited)
    pub max_frames: u32,
}

impl Default for CaptureConfig {
    fn default() -> Self {
        Self {
            mode: CaptureMode::SingleFrame,
            timeout_ms: 1000,
            enable_stats: true,
            max_frames: 0,
        }
    }
}
/// DMA-based capture interface for high-speed data transfer
pub trait DmaCapture {
    type Error;
    type DmaChannel;

    /// Start DMA transfer for frame capture
    fn start_dma_capture(
        &mut self,
        buffer: &mut [u8],
        width: u16,
        height: u16,
    ) -> impl core::future::Future<Output = Result<(), Self::Error>> + Send;

    /// Wait for DMA transfer completion
    fn wait_dma_complete(
        &mut self,
    ) -> impl core::future::Future<Output = Result<usize, Self::Error>> + Send;

    /// Stop DMA transfer
    fn stop_dma(&mut self) -> Result<(), Self::Error>;
}

/// High-speed frame capture using DMA
pub struct DmaFrameCapture<DMA> {
    dma: DMA,
    stats: CaptureStats,
}

impl<DMA> DmaFrameCapture<DMA>
where
    DMA: DmaCapture,
{
    /// Create a new DMA frame capture instance
    pub fn new(dma: DMA) -> Self {
        Self {
            dma,
            stats: CaptureStats::default(),
        }
    }

    /// Capture a frame using DMA
    pub async fn capture_frame_dma(
        &mut self,
        buffer: &mut [u8],
        width: u16,
        height: u16,
        timeout_ms: u32,
    ) -> CaptureResult<usize> {
        let start_time = Instant::now();
        self.stats.frame_start_time = Some(start_time);

        // Start DMA transfer
        self.dma
            .start_dma_capture(buffer, width, height)
            .await
            .map_err(|_| CaptureError::HardwareError)?;

        // Wait for completion with timeout
        let timeout = Duration::from_millis(timeout_ms as u64);
        let bytes_transferred =
            match select(self.dma.wait_dma_complete(), Timer::after(timeout)).await {
                Either::First(result) => result.map_err(|_| CaptureError::HardwareError)?,
                Either::Second(_) => {
                    // Timeout occurred, stop DMA
                    let _ = self.dma.stop_dma();
                    return Err(CaptureError::Timeout);
                }
            };

        self.stats.frame_duration = Some(Instant::now() - start_time);
        self.stats.frames_captured += 1;

        Ok(bytes_transferred)
    }

    /// Get capture statistics
    pub fn get_stats(&self) -> CaptureStats {
        self.stats
    }

    /// Reset capture statistics
    pub fn reset_stats(&mut self) {
        self.stats = CaptureStats::default();
    }
}

/// Double buffering support for continuous capture
pub struct DoubleBuffer<'a> {
    buffer_a: &'a mut [u8],
    buffer_b: &'a mut [u8],
    current_buffer: bool, // false = A, true = B
    width: u16,
    height: u16,
    bytes_per_pixel: u8,
}

impl<'a> DoubleBuffer<'a> {
    /// Create a new double buffer
    pub fn new(
        buffer_a: &'a mut [u8],
        buffer_b: &'a mut [u8],
        width: u16,
        height: u16,
        bytes_per_pixel: u8,
    ) -> Self {
        Self {
            buffer_a,
            buffer_b,
            current_buffer: false,
            width,
            height,
            bytes_per_pixel,
        }
    }

    /// Get the current capture buffer
    pub fn get_capture_buffer(&mut self) -> &mut [u8] {
        if self.current_buffer {
            self.buffer_b
        } else {
            self.buffer_a
        }
    }

    /// Get the current display buffer (the one not being captured to)
    pub fn get_display_buffer(&self) -> &[u8] {
        if self.current_buffer {
            self.buffer_a
        } else {
            self.buffer_b
        }
    }

    /// Swap buffers
    pub fn swap_buffers(&mut self) {
        self.current_buffer = !self.current_buffer;
    }

    /// Get buffer dimensions
    pub fn dimensions(&self) -> (u16, u16, u8) {
        (self.width, self.height, self.bytes_per_pixel)
    }
}
