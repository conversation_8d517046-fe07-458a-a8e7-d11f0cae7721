//! Configuration types and presets for the OV7670 camera sensor
//!
//! This module provides configuration structures and predefined settings
//! for different image formats and resolutions.

use defmt::Format;

/// Image output format
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum ImageFormat {
    /// YUV 4:2:2 format
    Yuv422,
    /// RGB565 format
    Rgb565,
    /// RGB555 format
    Rgb555,
    /// Bayer RAW format
    BayerRaw,
    /// Processed Bayer format
    ProcessedBayer,
}

/// Image resolution
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum Resolution {
    /// VGA resolution (640x480)
    Vga,
    /// QVGA resolution (320x240)
    Qvga,
    /// CIF resolution (352x288)
    Cif,
    /// QCIF resolution (176x144)
    Qcif,
}

impl Resolution {
    /// Get the width and height for this resolution
    pub const fn dimensions(&self) -> (u16, u16) {
        match self {
            Resolution::Vga => (640, 480),
            Resolution::Qvga => (320, 240),
            Resolution::Cif => (352, 288),
            Resolution::Qcif => (176, 144),
        }
    }

    /// Get the total number of pixels for this resolution
    pub const fn pixel_count(&self) -> u32 {
        let (w, h) = self.dimensions();
        w as u32 * h as u32
    }
}

/// Clock configuration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub struct ClockConfig {
    /// Use external clock (true) or internal PLL (false)
    pub use_external_clock: bool,
    /// Clock prescaler (0-63, only used with internal PLL)
    pub prescaler: u8,
}

impl Default for ClockConfig {
    fn default() -> Self {
        Self {
            use_external_clock: false,
            prescaler: 1,
        }
    }
}

/// Image processing configuration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub struct ImageProcessing {
    /// Enable automatic gain control
    pub auto_gain: bool,
    /// Enable automatic exposure control
    pub auto_exposure: bool,
    /// Enable automatic white balance
    pub auto_white_balance: bool,
    /// Mirror image horizontally
    pub mirror: bool,
    /// Flip image vertically
    pub flip: bool,
}

impl Default for ImageProcessing {
    fn default() -> Self {
        Self {
            auto_gain: true,
            auto_exposure: true,
            auto_white_balance: true,
            mirror: false,
            flip: false,
        }
    }
}

/// Complete camera configuration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub struct CameraConfig {
    /// Image output format
    pub format: ImageFormat,
    /// Image resolution
    pub resolution: Resolution,
    /// Clock configuration
    pub clock: ClockConfig,
    /// Image processing settings
    pub processing: ImageProcessing,
}

impl Default for CameraConfig {
    fn default() -> Self {
        Self {
            format: ImageFormat::Rgb565,
            resolution: Resolution::Qvga,
            clock: ClockConfig::default(),
            processing: ImageProcessing::default(),
        }
    }
}

impl CameraConfig {
    /// Create a new configuration with RGB565 format and QVGA resolution
    pub const fn rgb565_qvga() -> Self {
        Self {
            format: ImageFormat::Rgb565,
            resolution: Resolution::Qvga,
            clock: ClockConfig {
                use_external_clock: false,
                prescaler: 1,
            },
            processing: ImageProcessing {
                auto_gain: true,
                auto_exposure: true,
                auto_white_balance: true,
                mirror: false,
                flip: false,
            },
        }
    }

    /// Create a new configuration with YUV422 format and VGA resolution
    pub const fn yuv422_vga() -> Self {
        Self {
            format: ImageFormat::Yuv422,
            resolution: Resolution::Vga,
            clock: ClockConfig {
                use_external_clock: false,
                prescaler: 1,
            },
            processing: ImageProcessing {
                auto_gain: true,
                auto_exposure: true,
                auto_white_balance: true,
                mirror: false,
                flip: false,
            },
        }
    }

    /// Get the bytes per pixel for the current format
    pub const fn bytes_per_pixel(&self) -> u8 {
        match self.format {
            ImageFormat::Yuv422 => 2,
            ImageFormat::Rgb565 => 2,
            ImageFormat::Rgb555 => 2,
            ImageFormat::BayerRaw => 1,
            ImageFormat::ProcessedBayer => 1,
        }
    }

    /// Calculate the total frame size in bytes
    pub const fn frame_size_bytes(&self) -> u32 {
        let (w, h) = self.resolution.dimensions();
        w as u32 * h as u32 * self.bytes_per_pixel() as u32
    }

    /// Check if the configuration is valid
    pub const fn is_valid(&self) -> bool {
        // Check if prescaler is within valid range
        if self.clock.prescaler > 63 {
            return false;
        }

        // All other parameters are validated by their types
        true
    }

    /// Get the frame dimensions for this configuration
    pub const fn frame_dimensions(&self) -> (u16, u16) {
        self.resolution.dimensions()
    }

    /// Create a configuration optimized for low power consumption
    pub const fn low_power() -> Self {
        Self {
            format: ImageFormat::Yuv422,
            resolution: Resolution::Qcif, // Smallest resolution
            clock: ClockConfig {
                use_external_clock: false,
                prescaler: 4, // Slower clock
            },
            processing: ImageProcessing {
                auto_gain: false,
                auto_exposure: false,
                auto_white_balance: false,
                mirror: false,
                flip: false,
            },
        }
    }

    /// Create a configuration optimized for high quality
    pub const fn high_quality() -> Self {
        Self {
            format: ImageFormat::Rgb565,
            resolution: Resolution::Vga, // Highest resolution
            clock: ClockConfig {
                use_external_clock: false,
                prescaler: 1, // Fastest clock
            },
            processing: ImageProcessing {
                auto_gain: true,
                auto_exposure: true,
                auto_white_balance: true,
                mirror: false,
                flip: false,
            },
        }
    }
}
