#![no_std]

//! # OV7670 Camera Sensor Driver
//!
//! This crate provides a comprehensive async driver for the OV7670 camera sensor using the Embassy framework.
//! The driver is designed to be compatible with `embedded-hal` traits and provides a clean,
//! non-redundant API for camera configuration, image capture, and processing.
//!
//! ## Features
//!
//! - **Async/await support** using Embassy framework
//! - **Complete image capture** with VSYNC/HREF/PCLK synchronization
//! - **Multiple image formats**: YUV422, RGB565, RGB555, Bayer RAW, Processed Bayer
//! - **Multiple resolutions**: VGA (640x480), QVGA (320x240), CIF (352x288), QCIF (176x144)
//! - **Configurable image processing**: Auto Gain Control, Auto Exposure Control, Auto White Balance
//! - **Image transformations**: Mirror and flip support
//! - **High-speed DMA capture**: DMA-based data transfer for maximum performance
//! - **Double buffering**: Continuous capture with ping-pong buffers
//! - **Image processing utilities**: Format conversion, cropping, scaling, brightness/contrast adjustment
//! - **Hardware and software reset** support
//! - **Register-level access** for advanced configuration
//!
//! ## Basic Usage
//!
//! ```rust,no_run
//! use ov7670::{Ov7670, CameraConfig, ImageFormat, Resolution};
//! use embassy_time::Timer;
//!
//! # async fn example() -> Result<(), ov7670::Error<()>> {
//! // Create camera configuration
//! let config = CameraConfig {
//!     format: ImageFormat::Rgb565,
//!     resolution: Resolution::Qvga,
//!     ..Default::default()
//! };
//!
//! // Initialize camera (assuming i2c and reset_pin are available)
//! # let i2c = ();
//! # let reset_pin = ();
//! let mut camera = Ov7670::with_config(i2c, Some(reset_pin), config);
//! camera.init().await?;
//!
//! // Camera is now ready for use
//! let (width, height) = camera.frame_dimensions();
//! let frame_size = camera.frame_size_bytes();
//! # Ok(())
//! # }
//! ```
//!
//! ## Image Capture
//!
//! ```rust,no_run
//! use ov7670::{FrameSync, FrameBuffer, PixelDataReader};
//!
//! # struct MyDataPins;
//! # impl PixelDataReader for MyDataPins {
//! #     type Error = ();
//! #     fn read_pixel_data(&mut self) -> Result<u8, Self::Error> { Ok(0) }
//! # }
//! # async fn example() -> Result<(), ov7670::CaptureError> {
//! # let vsync_pin = ();
//! # let href_pin = ();
//! # let pclk_pin = ();
//! # let data_pins = MyDataPins;
//! // Create frame synchronization
//! let mut frame_sync = FrameSync::new(vsync_pin, href_pin, pclk_pin, data_pins);
//!
//! // Create frame buffer
//! let mut buffer = [0u8; 320 * 240 * 2]; // QVGA RGB565
//! let mut frame_buffer = FrameBuffer::new(&mut buffer, 320, 240, 2);
//!
//! // Capture a frame
//! frame_sync.capture_frame(&mut frame_buffer, 1000).await?;
//! # Ok(())
//! # }
//! ```

pub mod capture;
pub mod config;
pub mod driver;
pub mod error;
pub mod image;
pub mod registers;

// Re-export main types for convenience
pub use capture::{
    CaptureConfig, CaptureError, CaptureMode, CaptureResult, CaptureStats, DmaCapture,
    DmaFrameCapture, DoubleBuffer, FrameBuffer, FrameSync, PixelDataReader,
};
pub use config::{CameraConfig, ClockConfig, ImageFormat, ImageProcessing, Resolution};
pub use driver::{Ov7670, RegConfig};
pub use error::{Error, Result};
pub use image::{
    CropParams, FormatConverter, ImageError, ImageProcessor, ImageResult, Rgb565, ScaleParams,
    Yuv422,
};
