//! OV7670 camera sensor async driver
//!
//! This module provides the main driver implementation for the OV7670 camera sensor
//! with async/await support using Embassy framework.

use defmt::{Format, debug, info, warn};
use embassy_time::{Duration, Timer};
use embedded_hal::digital::OutputPin;
use embedded_hal_async::i2c::I2c;

use crate::config::{CameraConfig, ImageFormat, Resolution};
use crate::error::{Error, Result};
use crate::registers::{self, OV7670_I2C_ADDR};

/// Timing constants for camera operations
const RESET_DELAY_MS: u64 = 10;
const RESET_RECOVERY_MS: u64 = 10;
const SOFTWARE_RESET_DELAY_MS: u64 = 100;

/// Register configuration entry
#[derive(Debug, Clone, Copy, Format)]
pub struct RegConfig {
    reg: u8,
    val: u8,
}

/// OV7670 camera driver
pub struct Ov7670<I2C, RESET> {
    i2c: I2C,
    reset_pin: Option<RESET>,
    config: CameraConfig,
    initialized: bool,
}

impl<I2C, RESET, I2cError> Ov7670<I2C, RESET>
where
    I2C: I2c<Error = I2cError>,
    RESET: OutputPin,
{
    /// Create a new OV7670 driver instance
    pub fn new(i2c: I2C, reset_pin: Option<RESET>) -> Self {
        Self {
            i2c,
            reset_pin,
            config: CameraConfig::default(),
            initialized: false,
        }
    }

    /// Create a new OV7670 driver instance with custom configuration
    pub fn with_config(i2c: I2C, reset_pin: Option<RESET>, config: CameraConfig) -> Self {
        Self {
            i2c,
            reset_pin,
            config,
            initialized: false,
        }
    }

    /// Initialize the camera sensor
    pub async fn init(&mut self) -> Result<(), I2cError> {
        info!("Initializing OV7670 camera sensor");

        // Hardware reset if reset pin is available
        if let Some(ref mut reset_pin) = self.reset_pin {
            debug!("Performing hardware reset");
            reset_pin.set_low().map_err(|_| Error::HardwareError)?;
            Timer::after(Duration::from_millis(RESET_DELAY_MS)).await;
            reset_pin.set_high().map_err(|_| Error::HardwareError)?;
            Timer::after(Duration::from_millis(RESET_RECOVERY_MS)).await;
        }

        // Software reset
        debug!("Performing software reset");
        self.write_register(registers::reg::COM7, registers::com7::RESET)
            .await?;
        Timer::after(Duration::from_millis(SOFTWARE_RESET_DELAY_MS)).await;

        // Verify device ID
        self.verify_device_id().await?;

        // Apply initial configuration
        self.configure().await?;

        self.initialized = true;
        info!("OV7670 initialization complete");
        Ok(())
    }

    /// Verify that the connected device is an OV7670
    async fn verify_device_id(&mut self) -> Result<(), I2cError> {
        debug!("Verifying device ID");

        let pid = self.read_register(registers::reg::PID).await?;
        let ver = self.read_register(registers::reg::VER).await?;

        if pid != registers::id::PID_VALUE {
            warn!(
                "Invalid PID: expected 0x{:02X}, found 0x{:02X}",
                registers::id::PID_VALUE,
                pid
            );
            return Err(Error::InvalidDeviceId {
                expected: registers::id::PID_VALUE,
                found: pid,
            });
        }

        if ver != registers::id::VER_VALUE {
            warn!(
                "Invalid VER: expected 0x{:02X}, found 0x{:02X}",
                registers::id::VER_VALUE,
                ver
            );
            return Err(Error::InvalidDeviceId {
                expected: registers::id::VER_VALUE,
                found: ver,
            });
        }

        debug!("Device ID verified: PID=0x{:02X}, VER=0x{:02X}", pid, ver);
        Ok(())
    }

    /// Configure the camera with current settings
    async fn configure(&mut self) -> Result<(), I2cError> {
        debug!("Configuring camera with settings: {:?}", self.config);

        // Apply format-specific configuration
        self.configure_format().await?;

        // Apply resolution-specific configuration
        self.configure_resolution().await?;

        // Apply clock configuration
        self.configure_clock().await?;

        // Apply image processing settings
        self.configure_processing().await?;

        // Apply common settings
        self.apply_common_settings().await?;

        debug!("Camera configuration complete");
        Ok(())
    }

    /// Configure output format
    async fn configure_format(&mut self) -> Result<(), I2cError> {
        let (com7_val, com15_val, tslb_val) = match self.config.format {
            ImageFormat::Yuv422 => (registers::com7::YUV, 0x00, 0x00),
            ImageFormat::Rgb565 => (registers::com7::RGB, registers::com15::RGB565, 0x00),
            ImageFormat::Rgb555 => (registers::com7::RGB, registers::com15::RGB555, 0x00),
            ImageFormat::BayerRaw => (registers::com7::BAYER, 0x00, 0x00),
            ImageFormat::ProcessedBayer => (registers::com7::PBAYER, 0x00, 0x00),
        };

        self.write_register(registers::reg::COM7, com7_val).await?;
        self.write_register(registers::reg::COM15, com15_val)
            .await?;
        self.write_register(registers::reg::TSLB, tslb_val).await?;

        debug!("Format configured: {:?}", self.config.format);
        Ok(())
    }
    /// Configure resolution
    async fn configure_resolution(&mut self) -> Result<(), I2cError> {
        let com7_res = match self.config.resolution {
            Resolution::Vga => registers::com7::FMT_VGA,
            Resolution::Qvga => registers::com7::FMT_QVGA,
            Resolution::Cif => registers::com7::FMT_CIF,
            Resolution::Qcif => registers::com7::FMT_QCIF,
        };

        // Read current COM7 value and update only resolution bits
        let mut com7_val = self.read_register(registers::reg::COM7).await?;
        com7_val = (com7_val & !registers::com7::FMT_MASK) | com7_res;
        self.write_register(registers::reg::COM7, com7_val).await?;

        debug!("Resolution configured: {:?}", self.config.resolution);
        Ok(())
    }

    /// Configure clock settings
    async fn configure_clock(&mut self) -> Result<(), I2cError> {
        let mut clkrc_val = self.config.clock.prescaler & registers::clkrc::CLK_SCALE;

        if self.config.clock.use_external_clock {
            clkrc_val |= registers::clkrc::CLK_EXT;
        }

        self.write_register(registers::reg::CLKRC, clkrc_val)
            .await?;
        debug!(
            "Clock configured: prescaler={}, external={}",
            self.config.clock.prescaler, self.config.clock.use_external_clock
        );
        Ok(())
    }

    /// Configure image processing settings
    async fn configure_processing(&mut self) -> Result<(), I2cError> {
        // Configure COM8 for auto controls
        let mut com8_val = 0;
        if self.config.processing.auto_gain {
            com8_val |= registers::com8::AGC_EN;
        }
        if self.config.processing.auto_exposure {
            com8_val |= registers::com8::AEC_EN;
        }
        if self.config.processing.auto_white_balance {
            com8_val |= registers::com8::AWB_EN;
        }
        self.write_register(registers::reg::COM8, com8_val).await?;

        // Configure MVFP for mirror/flip
        let mut mvfp_val = 0;
        if self.config.processing.mirror {
            mvfp_val |= registers::mvfp::MIRROR;
        }
        if self.config.processing.flip {
            mvfp_val |= registers::mvfp::FLIP;
        }
        self.write_register(registers::reg::MVFP, mvfp_val).await?;

        debug!("Image processing configured");
        Ok(())
    }

    /// Apply common camera settings
    async fn apply_common_settings(&mut self) -> Result<(), I2cError> {
        // Common register settings for stable operation
        let common_settings = [
            RegConfig {
                reg: registers::reg::COM3,
                val: 0x00,
            },
            RegConfig {
                reg: registers::reg::COM14,
                val: 0x00,
            },
            RegConfig {
                reg: registers::reg::SCALING_XSC,
                val: 0x3A,
            },
            RegConfig {
                reg: registers::reg::SCALING_YSC,
                val: 0x35,
            },
            RegConfig {
                reg: registers::reg::SCALING_DCWCTR,
                val: 0x11,
            },
            RegConfig {
                reg: registers::reg::SCALING_PCLK_DIV,
                val: 0xF0,
            },
            RegConfig {
                reg: registers::reg::SCALING_PCLK_DELAY,
                val: 0x02,
            },
        ];

        for setting in &common_settings {
            self.write_register(setting.reg, setting.val).await?;
        }

        debug!("Common settings applied");
        Ok(())
    }

    /// Update camera configuration
    pub async fn set_config(&mut self, config: CameraConfig) -> Result<(), I2cError> {
        if !self.initialized {
            return Err(Error::NotInitialized);
        }

        self.config = config;
        self.configure().await?;
        Ok(())
    }

    /// Get current camera configuration
    pub fn get_config(&self) -> CameraConfig {
        self.config
    }

    /// Check if the camera is initialized
    pub fn is_initialized(&self) -> bool {
        self.initialized
    }

    /// Read a single register value
    async fn read_register(&mut self, reg: u8) -> Result<u8, I2cError> {
        let mut buffer = [0u8; 1];
        self.i2c
            .write_read(OV7670_I2C_ADDR, &[reg], &mut buffer)
            .await?;
        Ok(buffer[0])
    }

    /// Write a single register value
    async fn write_register(&mut self, reg: u8, val: u8) -> Result<(), I2cError> {
        self.i2c.write(OV7670_I2C_ADDR, &[reg, val]).await?;
        Ok(())
    }

    /// Read multiple registers
    pub async fn read_registers(
        &mut self,
        start_reg: u8,
        buffer: &mut [u8],
    ) -> Result<(), I2cError> {
        if !self.initialized {
            return Err(Error::NotInitialized);
        }

        for (i, byte) in buffer.iter_mut().enumerate() {
            *byte = self.read_register(start_reg + i as u8).await?;
        }
        Ok(())
    }

    /// Write multiple registers
    pub async fn write_registers(&mut self, configs: &[RegConfig]) -> Result<(), I2cError> {
        if !self.initialized {
            return Err(Error::NotInitialized);
        }

        for config in configs {
            self.write_register(config.reg, config.val).await?;
        }
        Ok(())
    }

    /// Get frame dimensions for current configuration
    pub fn frame_dimensions(&self) -> (u16, u16) {
        self.config.resolution.dimensions()
    }

    /// Get frame size in bytes for current configuration
    pub fn frame_size_bytes(&self) -> u32 {
        self.config.frame_size_bytes()
    }

    /// Release the I2C bus and reset pin
    pub fn release(self) -> (I2C, Option<RESET>) {
        (self.i2c, self.reset_pin)
    }
}
