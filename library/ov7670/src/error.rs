//! Error types for the OV7670 driver
//!
//! This module defines all possible errors that can occur when using the OV7670 driver.

use defmt::Format;

/// Errors that can occur when using the OV7670 driver
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Format)]
pub enum Error<I2cError> {
    /// I2C communication error
    I2c(I2cError),
    /// Invalid device ID detected
    InvalidDeviceId { expected: u8, found: u8 },
    /// Timeout occurred during operation
    Timeout,
    /// Invalid configuration parameter
    InvalidConfig,
    /// Device not initialized
    NotInitialized,
    /// Frame capture error
    CaptureError,
    /// Buffer too small for the requested operation
    BufferTooSmall,
    /// Invalid image format
    InvalidFormat,
    /// Hardware error detected
    HardwareError,
}

impl<I2cError> From<I2cError> for Error<I2cError> {
    fn from(error: I2cError) -> Self {
        Error::I2c(error)
    }
}

impl<I2cError> Error<I2cError> {
    /// Check if this is an I2C communication error
    pub const fn is_i2c_error(&self) -> bool {
        matches!(self, Error::I2c(_))
    }

    /// Check if this is a timeout error
    pub const fn is_timeout(&self) -> bool {
        matches!(self, Error::Timeout)
    }

    /// Check if this is a configuration error
    pub const fn is_config_error(&self) -> bool {
        matches!(self, Error::InvalidConfig)
    }

    /// Check if this is a hardware-related error
    pub const fn is_hardware_error(&self) -> bool {
        matches!(self, Error::HardwareError)
    }

    /// Check if this is a device initialization error
    pub const fn is_initialization_error(&self) -> bool {
        matches!(self, Error::NotInitialized | Error::InvalidDeviceId { .. })
    }

    /// Get a human-readable description of the error
    pub fn description(&self) -> &'static str {
        match self {
            Error::I2c(_) => "I2C communication error",
            Error::InvalidDeviceId { .. } => "Invalid device ID detected",
            Error::Timeout => "Operation timed out",
            Error::InvalidConfig => "Invalid configuration parameter",
            Error::NotInitialized => "Device not initialized",
            Error::CaptureError => "Frame capture error",
            Error::BufferTooSmall => "Buffer too small for operation",
            Error::InvalidFormat => "Invalid image format",
            Error::HardwareError => "Hardware error detected",
        }
    }
}

/// Result type for OV7670 operations
pub type Result<T, I2cError> = core::result::Result<T, Error<I2cError>>;
