# OV7670 Camera Sensor Driver

An async driver for the OV7670 camera sensor using the Embassy framework, designed to be compatible with `embedded-hal` traits.

## Features

- **Async/await support** using Embassy framework
- **Complete image capture**: Frame capture with VSYNC/HREF/PCLK synchronization
- **Multiple image formats**: YUV422, RGB565, RGB555, Bayer RAW, Processed Bayer
- **Multiple resolutions**: VGA (640x480), QVGA (320x240), CIF (352x288), QCIF (176x144)
- **Configurable image processing**: Auto Gain Control, Auto Exposure Control, Auto White Balance
- **Image transformations**: Mirror and flip support
- **Frame synchronization**: Hardware-based frame and line synchronization
- **High-speed DMA capture**: DMA-based data transfer for maximum performance
- **Double buffering**: Continuous capture with ping-pong buffers
- **Image processing utilities**: Format conversion, cropping, scaling, brightness/contrast adjustment
- **Flexible buffering**: User-provided buffers with overflow protection
- **Capture statistics**: Frame timing and error tracking
- **Hardware and software reset** support
- **Register-level access** for advanced configuration
- **Clean, non-redundant API** with structured configuration

## Usage

Add this to your `Cargo.toml`:

```toml
[dependencies]
ov7670 = { path = "path/to/this/crate" }
embassy-time = "0.4.0"
embedded-hal = "1.0.0"
embedded-hal-async = "1.0.0"
```

### Basic Example

```rust
use ov7670::{Ov7670, CameraConfig, ImageFormat, Resolution};
use embassy_time::Timer;

// Create camera configuration
let config = CameraConfig {
    format: ImageFormat::Rgb565,
    resolution: Resolution::Qvga,
    ..Default::default()
};

// Initialize camera (assuming i2c and reset_pin are available)
let mut camera = Ov7670::with_config(i2c, Some(reset_pin), config);
camera.init().await?;

// Get frame information
let (width, height) = camera.frame_dimensions();
let frame_size = camera.frame_size_bytes();

println!("Camera initialized: {}x{}, {} bytes per frame", width, height, frame_size);
```

### Image Capture Example

```rust
use ov7670::{FrameSync, FrameBuffer, PixelDataReader};

// Implement PixelDataReader for your hardware
struct MyDataPins {
    // Your 8-bit parallel data pins
}

impl PixelDataReader for MyDataPins {
    type Error = ();

    fn read_pixel_data(&mut self) -> Result<u8, Self::Error> {
        // Read 8-bit data from your parallel pins
        Ok(0) // Replace with actual pin reading
    }
}

// Create frame synchronization
let mut frame_sync = FrameSync::new(vsync_pin, href_pin, pclk_pin, data_pins);

// Create frame buffer
let mut buffer = [0u8; 320 * 240 * 2]; // QVGA RGB565
let mut frame_buffer = FrameBuffer::new(&mut buffer, 320, 240, 2);

// Capture a frame
frame_sync.capture_frame(&mut frame_buffer, 1000).await?;

println!("Captured frame {} at {:?}", frame_buffer.sequence, frame_buffer.timestamp);
```

### DMA Capture Example

```rust
use ov7670::{DmaFrameCapture, DmaCapture};

// Implement DmaCapture for your hardware
struct MyDmaCapture {
    // Your DMA channel and configuration
}

impl DmaCapture for MyDmaCapture {
    type Error = ();
    type DmaChannel = ();

    async fn start_dma_capture(
        &mut self,
        buffer: &mut [u8],
        width: u16,
        height: u16,
    ) -> Result<(), Self::Error> {
        // Configure and start DMA transfer
        Ok(())
    }

    async fn wait_dma_complete(&mut self) -> Result<usize, Self::Error> {
        // Wait for DMA completion and return bytes transferred
        Ok(320 * 240 * 2)
    }

    fn stop_dma(&mut self) -> Result<(), Self::Error> {
        // Stop DMA transfer
        Ok(())
    }
}

// High-speed capture using DMA
let mut dma_capture = DmaFrameCapture::new(my_dma);
let mut buffer = [0u8; 320 * 240 * 2];

let bytes_captured = dma_capture
    .capture_frame_dma(&mut buffer, 320, 240, 1000)
    .await?;

println!("DMA captured {} bytes", bytes_captured);
```

### Double Buffer Example

```rust
use ov7670::DoubleBuffer;

// Create double buffer for continuous capture
let mut buffer_a = [0u8; 320 * 240 * 2];
let mut buffer_b = [0u8; 320 * 240 * 2];

let mut double_buffer = DoubleBuffer::new(
    &mut buffer_a,
    &mut buffer_b,
    320, 240, 2
);

loop {
    // Capture to current buffer
    let capture_buf = double_buffer.get_capture_buffer();
    // ... perform capture ...

    // Process previous buffer while capturing
    let display_buf = double_buffer.get_display_buffer();
    // ... process/display image ...

    // Swap buffers
    double_buffer.swap_buffers();
}
```

### Image Processing Example

```rust
use ov7670::{FormatConverter, ImageProcessor, CropParams, ScaleParams, Rgb565, Yuv422};

// Convert YUV422 to RGB565
let mut yuv_buffer = [0u8; 320 * 240 * 2]; // YUV422 data
let mut rgb_buffer = [0u8; 320 * 240 * 2]; // RGB565 output

FormatConverter::yuv422_to_rgb565(&yuv_buffer, &mut rgb_buffer, 320, 240)?;

// Crop image
let mut cropped_buffer = [0u8; 160 * 120 * 2];
let crop_params = CropParams {
    src_width: 320,
    src_height: 240,
    crop_x: 80,
    crop_y: 60,
    crop_width: 160,
    crop_height: 120,
    bytes_per_pixel: 2,
};
ImageProcessor::crop(&rgb_buffer, &mut cropped_buffer, &crop_params)?;

// Scale image
let mut scaled_buffer = [0u8; 640 * 480 * 2];
let scale_params = ScaleParams {
    src_width: 160,
    src_height: 120,
    dst_width: 640,
    dst_height: 480,
    bytes_per_pixel: 2,
};
ImageProcessor::scale_nearest(&cropped_buffer, &mut scaled_buffer, &scale_params)?;

// Apply image effects
ImageProcessor::mirror_horizontal(&mut scaled_buffer, 640, 480, 2)?;
ImageProcessor::adjust_brightness(&mut scaled_buffer, 640, 480, 2, 50)?;
ImageProcessor::adjust_contrast(&mut scaled_buffer, 640, 480, 2, 307)?; // 1.2x contrast (307/256)
```

### Configuration Options

```rust
use ov7670::{CameraConfig, ImageFormat, Resolution, ImageProcessing, ClockConfig};

let config = CameraConfig {
    format: ImageFormat::Yuv422,
    resolution: Resolution::Vga,
    clock: ClockConfig {
        use_external_clock: false,
        prescaler: 2,
    },
    processing: ImageProcessing {
        auto_gain: true,
        auto_exposure: true,
        auto_white_balance: true,
        mirror: false,
        flip: false,
    },
};

// Validate configuration
assert!(config.is_valid());
```

### Predefined Configurations

```rust
// RGB565 QVGA configuration
let config = CameraConfig::rgb565_qvga();

// YUV422 VGA configuration
let config = CameraConfig::yuv422_vga();

// Low power configuration (QCIF, slower clock, no auto processing)
let config = CameraConfig::low_power();

// High quality configuration (VGA, fast clock, all auto processing)
let config = CameraConfig::high_quality();
```

### Runtime Configuration Changes

```rust
// Change configuration after initialization
let new_config = CameraConfig {
    format: ImageFormat::Rgb565,
    resolution: Resolution::Qcif,
    ..camera.get_config()
};

camera.set_config(new_config).await?;
```

### Register Access

```rust
// Read multiple registers
let mut buffer = [0u8; 4];
camera.read_registers(0x0A, &mut buffer).await?;

// Write custom register configurations
use ov7670::driver::RegConfig;
let custom_settings = [
    RegConfig { reg: 0x12, val: 0x80 }, // Reset
    RegConfig { reg: 0x11, val: 0x01 }, // Clock prescaler
];
camera.write_registers(&custom_settings).await?;
```

## Supported Image Formats

| Format | Description | Bytes per Pixel |
|--------|-------------|-----------------|
| YUV422 | YUV 4:2:2 format | 2 |
| RGB565 | 16-bit RGB (5-6-5) | 2 |
| RGB555 | 15-bit RGB (5-5-5) | 2 |
| BayerRaw | Raw Bayer pattern | 1 |
| ProcessedBayer | Processed Bayer | 1 |

## Supported Resolutions

| Resolution | Dimensions | Total Pixels |
|------------|------------|--------------|
| VGA | 640 × 480 | 307,200 |
| QVGA | 320 × 240 | 76,800 |
| CIF | 352 × 288 | 101,376 |
| QCIF | 176 × 144 | 25,344 |

## Hardware Requirements

- OV7670 camera sensor module
- I2C bus for register configuration
- Optional: Reset pin for hardware reset
- Optional: External clock source

## License

This project is licensed under the MIT License - see the LICENSE file for details.
