[package]
authors = ["Alddp <<EMAIL>>"]
edition = "2024"
name = "mpu6500"
version = "0.1.0"
license = "MIT OR Apache-2.0"
repository = "https://github.com/Alddp/mpu6500"
documentation = "https://docs.rs/mpu6500"
description = "高效、易用的 MPU6500 六轴传感器 Rust 驱动库，支持 async/await"
readme = "README.md"
categories = ["embedded", "hardware-support", "no-std"]
keywords = ["mpu6500", "imu", "embedded-hal", "async"]

[dependencies]
embedded-hal-async = { version = "1.0.0" }
embassy-time = { version = "0.4.0", git = "https://github.com/embassy-rs/embassy.git" }
embedded-hal = { version = "1.0.0" }
libm = "0.2.15"
