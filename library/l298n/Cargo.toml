[package]
name = "l298n"
version = "0.1.0"
authors = ["Alddp <<EMAIL>>"]
edition = "2024"
description = ""
license = "MIT OR Apache-2.0"
readme = "README.md"

[dependencies]

embassy-stm32 = { git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
    "stm32f103c8",
    "unstable-pac",
    "memory-x",
    "time-driver-any",
] }
embedded-hal = {version = "0.2.7"}

[lib]
name= "l298n"
test = false
bench = false
