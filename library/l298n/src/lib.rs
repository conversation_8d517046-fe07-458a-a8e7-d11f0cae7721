#![no_std]
#![no_main]

//! Manages a new L298N a Dual H-Bridge Motor Controller module

#![deny(
    missing_docs,
    // missing_debug_implementations,
    missing_copy_implementations,
    trivial_casts,
    trivial_numeric_casts,
    unstable_features,
    unused_import_braces,
    unused_qualifications,
    warnings
)]
#![allow(dead_code)]

use embassy_stm32::gpio::Output;
use embassy_stm32::timer::{GeneralInstance4Channel, simple_pwm::SimplePwmChannel};

/// Struct for L298N. Two enable inputs are provided to enable or disable the device
/// independently of the input signals. The emitters of the lower transistors of each
/// bridge are connected together and the corresponding external terminal can be used
/// for the connection of an external sensing resistor. An additional supply input is
/// provided so that the logic works at a lower voltage.
pub struct L298N<'a, TA, TB>
where
    TA: GeneralInstance4Channel,
    TB: GeneralInstance4Channel,
{
    /// motor A
    pub a: Motor<'a, TA>,
    /// motor B
    pub b: Motor<'a, TB>,
}

impl<'a, TA, TB> L298N<'a, TA, TB>
where
    TA: GeneralInstance4Channel,
    TB: GeneralInstance4Channel,
{
    /// 创建一个新的 L298N 电机驱动实例
    pub fn new(
        ina1: Output<'a>,
        ina2: Output<'a>,
        mut pwm_a: SimplePwmChannel<'a, TA>,
        inb1: Output<'a>,
        inb2: Output<'a>,
        mut pwm_b: SimplePwmChannel<'a, TB>,
    ) -> L298N<'a, TA, TB> {
        if !pwm_a.is_enabled() {
            pwm_a.enable();
        }
        if !pwm_b.is_enabled() {
            pwm_b.enable();
        };

        L298N {
            a: Motor::new(ina1, ina2, pwm_a),
            b: Motor::new(inb1, inb2, pwm_b),
        }
    }
}

/// Struct for single bridge
pub struct Motor<'a, T: GeneralInstance4Channel> {
    in1: Output<'a>,
    in2: Output<'a>,
    pwm: SimplePwmChannel<'a, T>,
}

impl<'a, T: GeneralInstance4Channel> Motor<'a, T> {
    /// Creates a new single `Motor` controller
    pub fn new(in1: Output<'a>, in2: Output<'a>, pwm: SimplePwmChannel<'a, T>) -> Motor<'a, T> {
        Motor { in1, in2, pwm }
    }

    /// Brakes the motor - Fast Motor Stop
    /// with Ven = H then C = D Fast Motor Stop
    pub fn brake(&mut self) -> &mut Self {
        self.in1.set_high();
        self.in2.set_high();
        self
    }

    /// Stops the motor - Free Running Motor Stop
    /// Ven = L then with C = X ; D = X
    pub fn stop(&mut self) -> &mut Self {
        self.in1.set_high();
        self.in2.set_high();
        self
    }

    /// Makes the motor forward direction
    /// with Ven = H then C = H ; D = L Forward
    pub fn forward(&mut self) -> &mut Self {
        self.in1.set_low();
        self.in2.set_high();
        self
    }

    /// Makes the motor reverse direction
    /// with Ven = H then C = L ; D = H Reverse
    pub fn reverse(&mut self) -> &mut Self {
        self.in1.set_high();
        self.in2.set_low();
        self
    }

    /// Returns the maximum
    pub fn get_max_duty(&self) -> u16 {
        self.pwm.max_duty_cycle()
    }

    /// Changes the motor speed
    pub fn set_duty(&mut self, duty: u16) -> &mut Self {
        self.pwm.set_duty_cycle(duty);
        self
    }
    /// set duty percent
    pub fn set_duty_percent(&mut self, percent: u8) {
        self.pwm.set_duty_cycle_percent(percent);
    }
}
