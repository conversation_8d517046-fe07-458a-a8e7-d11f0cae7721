[package]
name = "uln2003-async"
version = "0.1.0"
authors = ["Your Name <<EMAIL>>"]
edition = "2024"
description = "A platform-agnostic asynchronous driver for the 28BYJ-48 stepper motor with ULN2003 driver, compatible with Embassy and embedded-hal."
license = "MIT OR Apache-2.0"
repository = "https://github.com/your-github-username/your-repo-name"
documentation = "https://docs.rs/uln2003-async"
readme = "README.md"
keywords = [
    "stepper",
    "motor",
    "ULN2003",
    "28BYJ-48",
    "embassy",
    "embedded-hal",
    "no-std",
    "async",
]
categories = ["embedded", "hardware-support", "no-std", "asynchronous"]

[dependencies]
embedded-hal = "1.0.0"
embassy-time = { version = "0.4.0", git = "https://github.com/embassy-rs/embassy.git" }
