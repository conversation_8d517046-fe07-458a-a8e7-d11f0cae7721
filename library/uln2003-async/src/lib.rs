#![no_std]

use embassy_time::{Duration, Timer};
use embedded_hal::digital::OutputPin;

pub struct Uln2003<IN1, IN2, IN3, IN4>
where
    IN1: OutputPin,
    IN2: OutputPin<Error = IN1::Error>,
    IN3: OutputPin<Error = IN1::Error>,
    IN4: OutputPin<Error = IN1::Error>,
{
    in1: IN1,
    in2: IN2,
    in3: IN3,
    in4: IN4,
    step_delay: Duration,
    current_sequence_idx: u8,
}

impl<IN1, IN2, IN3, IN4> Uln2003<IN1, IN2, IN3, IN4>
where
    IN1: OutputPin,
    IN2: OutputPin<Error = IN1::Error>,
    IN3: OutputPin<Error = IN1::Error>,
    IN4: OutputPin<Error = IN1::Error>,
{
    /// 创建一个新的ULN2003驱动实例。
    ///
    /// `in1` 到 `in4` 是已经初始化并配置为输出模式的 GPIO 引脚。
    ///
    /// 初始步进延时默认为 2毫秒。
    pub fn new(in1: IN1, in2: IN2, in3: IN3, in4: IN4) -> Self {
        Self {
            in1,
            in2,
            in3,
            in4,
            step_delay: Duration::from_millis(2),
            current_sequence_idx: 0, // 从步进序列的第一个状态开始
        }
    }

    /// 设置步进电机每步之间的延时。
    pub fn set_delay(&mut self, delay: Duration) {
        self.step_delay = delay;
    }

    /// 内部方法：设置电机到指定的步序模式。
    ///
    /// `step_index` 是步进序列的索引 (0-7)。
    /// 返回 `Result<(), E>`，其中 `E` 是 GPIO 操作可能产生的错误类型。
    async fn set_step(&mut self, step_index: u8) -> Result<(), IN1::Error> {
        // 28BYJ-48 半步进序列 (8个状态)
        const HALF_STEP_SEQUENCE: [[bool; 4]; 8] = [
            [true, false, false, false], // 步 0
            [true, true, false, false],  // 步 1
            [false, true, false, false], // 步 2
            [false, true, true, false],  // 步 3
            [false, false, true, false], // 步 4
            [false, false, true, true],  // 步 5
            [false, false, false, true], // 步 6
            [true, false, false, true],  // 步 7
        ];

        if let Some(pattern) = HALF_STEP_SEQUENCE.get(step_index as usize) {
            // 使用 set_high()/set_low() 方法，这是 embedded-hal v1.0.0 的标准用法
            if pattern[0] {
                self.in1.set_high()?;
            } else {
                self.in1.set_low()?;
            }
            if pattern[1] {
                self.in2.set_high()?;
            } else {
                self.in2.set_low()?;
            }
            if pattern[2] {
                self.in3.set_high()?;
            } else {
                self.in3.set_low()?;
            }
            if pattern[3] {
                self.in4.set_high()?;
            } else {
                self.in4.set_low()?;
            }
        }
        Ok(())
    }

    /// 旋转电机指定步数（正向）。
    ///
    /// `num_steps`: 旋转的步数。
    /// 返回 `Result<(), E>`，其中 `E` 是 GPIO 操作可能产生的错误类型。
    pub async fn step_forward(&mut self, num_steps: u32) -> Result<(), IN1::Error> {
        let sequence_len = 8; // 半步进序列的长度

        for _ in 0..num_steps {
            self.current_sequence_idx = (self.current_sequence_idx + 1) % sequence_len;
            self.set_step(self.current_sequence_idx).await?;
            Timer::after(self.step_delay).await;
        }
        self.stop().await?; // 完成步进后停止电机，关闭线圈
        Ok(())
    }

    /// 旋转电机指定步数（反向）。
    ///
    /// `num_steps`: 旋转的步数。
    /// 返回 `Result<(), E>`，其中 `E` 是 GPIO 操作可能产生的错误类型。
    pub async fn step_backward(&mut self, num_steps: u32) -> Result<(), IN1::Error> {
        let sequence_len = 8;

        for _ in 0..num_steps {
            // 计算前一个索引，处理无符号整数的环绕
            self.current_sequence_idx =
                (self.current_sequence_idx + sequence_len - 1) % sequence_len;
            self.set_step(self.current_sequence_idx).await?;
            Timer::after(self.step_delay).await;
        }
        self.stop().await?; // 完成步进后停止电机，关闭线圈
        Ok(())
    }

    /// 停止电机，关闭所有线圈以减少功耗和发热。
    /// 返回 `Result<(), E>`，其中 `E` 是 GPIO 操作可能产生的错误类型。
    pub async fn stop(&mut self) -> Result<(), IN1::Error> {
        self.in1.set_low()?;
        self.in2.set_low()?;
        self.in3.set_low()?;
        self.in4.set_low()?;
        Ok(())
    }
}
