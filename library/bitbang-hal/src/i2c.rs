/*!
  # Synchronous implementation of embedded-hal I2C traits based on GPIO bitbang

  This implementation consumes the following hardware resources:
  - A periodic timer to mark clock cycles
  - Two GPIO pins for SDA and SCL lines.

  Note that the current implementation does not support I2C clock stretching.

  ## Hardware requirements

  1. Configure GPIO pins as Open-Drain outputs.
  2. Configure timer frequency to be twice the desired I2C clock frequency.

  ## Blue Pill example

  Here is a sample code for LM75A I2C temperature sensor
  on Blue Pill or any other stm32f1xx board:

  ```no_run
    use stm32f1xx_hal as hal;
    use hal::{prelude::*, timer::Timer, stm32};
    use lm75::{Lm75, SlaveAddr};
    use bitbang_hal;

    // ...

    let pdev = stm32::Peripherals::take().unwrap();

    let mut flash = pdev.FLASH.constrain();
    let mut rcc = pdev.RCC.constrain();
    let mut gpioa = pdev.GPIOA.split(&mut rcc.apb2);

    let clocks = rcc
        .cfgr
        .use_hse(8.mhz())
        .sysclk(32.mhz())
        .pclk1(16.mhz())
        .freeze(&mut flash.acr);

    let tmr = Timer::tim3(pdev.TIM3, &clocks, &mut rcc.apb1).start_count_down(200.khz());
    let scl = gpioa.pa1.into_open_drain_output(&mut gpioa.crl);
    let sda = gpioa.pa2.into_open_drain_output(&mut gpioa.crl);

    let i2c = bitbang_hal::i2c::I2cBB::new(scl, sda, tmr);
    let mut sensor = Lm75::new(i2c, SlaveAddr::default());
    let temp = sensor.read_temperature().unwrap();

    //...
  ```
*/

use embedded_hal::delay::DelayNs;
use embedded_hal::digital::{InputPin, OutputPin};
use embedded_hal::i2c::{ErrorKind, ErrorType, I2c, Operation, SevenBitAddress};

#[cfg(feature = "async")]
use embedded_hal_async::delay::DelayNs as AsyncDelayNs;
#[cfg(feature = "async")]
use embedded_hal_async::i2c::{I2c as AsyncI2c, Operation as AsyncOperation};

/// I2C error
#[derive(Debug, Eq, PartialEq)]
pub enum Error<E> {
    /// GPIO error
    Bus(E),
    /// No ack received
    NoAck,
    /// Invalid input
    InvalidData,
}

impl<E: core::fmt::Debug> embedded_hal::i2c::Error for Error<E> {
    fn kind(&self) -> ErrorKind {
        match self {
            Error::Bus(_) => ErrorKind::Bus,
            Error::NoAck => {
                ErrorKind::NoAcknowledge(embedded_hal::i2c::NoAcknowledgeSource::Unknown)
            }
            Error::InvalidData => ErrorKind::Other,
        }
    }
}

/// Bit banging I2C device
pub struct I2cBB<SCL, SDA, DELAY>
where
    SCL: OutputPin,
    SDA: OutputPin + InputPin,
    DELAY: DelayNs,
{
    scl: SCL,
    sda: SDA,
    delay: DELAY,
    clock_period_ns: u32,
}

impl<SCL, SDA, DELAY, E> I2cBB<SCL, SDA, DELAY>
where
    SCL: OutputPin<Error = E>,
    SDA: OutputPin<Error = E> + InputPin<Error = E>,
    DELAY: DelayNs,
    E: core::fmt::Debug,
{
    /// Create instance
    pub fn new(scl: SCL, sda: SDA, delay: DELAY, clock_frequency_hz: u32) -> Self {
        let clock_period_ns = 1_000_000_000 / clock_frequency_hz;
        I2cBB {
            scl,
            sda,
            delay,
            clock_period_ns,
        }
    }

    /// Send a raw I2C start.
    ///
    /// **This is a low-level control function.** For normal I2C devices,
    /// please use the embedded-hal traits [Read], [Write], or
    /// [WriteRead].
    pub fn raw_i2c_start(&mut self) -> Result<(), crate::i2c::Error<E>> {
        self.set_scl_high()?;
        self.set_sda_high()?;
        self.wait_for_clk();

        self.set_sda_low()?;
        self.wait_for_clk();

        self.set_scl_low()?;
        self.wait_for_clk();

        Ok(())
    }

    /// Send a raw I2C stop.
    ///
    /// **This is a low-level control function.** For normal I2C devices,
    /// please use the embedded-hal traits [Read], [Write], or
    /// [WriteRead].
    pub fn raw_i2c_stop(&mut self) -> Result<(), crate::i2c::Error<E>> {
        self.set_scl_high()?;
        self.wait_for_clk();

        self.set_sda_high()?;
        self.wait_for_clk();

        Ok(())
    }

    fn i2c_is_ack(&mut self) -> Result<bool, crate::i2c::Error<E>> {
        self.set_sda_high()?;
        self.set_scl_high()?;
        self.wait_for_clk();

        let ack = self.sda.is_low().map_err(Error::Bus)?;

        self.set_scl_low()?;
        self.set_sda_low()?;
        self.wait_for_clk();

        Ok(ack)
    }

    fn i2c_read_byte(&mut self, should_send_ack: bool) -> Result<u8, crate::i2c::Error<E>> {
        let mut byte: u8 = 0;

        self.set_sda_high()?;

        for bit_offset in 0..8 {
            self.set_scl_high()?;
            self.wait_for_clk();

            if self.sda.is_high().map_err(Error::Bus)? {
                byte |= 1 << (7 - bit_offset);
            }

            self.set_scl_low()?;
            self.wait_for_clk();
        }

        if should_send_ack {
            self.set_sda_low()?;
        } else {
            self.set_sda_high()?;
        }

        self.set_scl_high()?;
        self.wait_for_clk();

        self.set_scl_low()?;
        self.set_sda_low()?;
        self.wait_for_clk();

        Ok(byte)
    }

    fn i2c_write_byte(&mut self, byte: u8) -> Result<(), crate::i2c::Error<E>> {
        for bit_offset in 0..8 {
            let out_bit = (byte >> (7 - bit_offset)) & 0b1;

            if out_bit == 1 {
                self.set_sda_high()?;
            } else {
                self.set_sda_low()?;
            }

            self.set_scl_high()?;
            self.wait_for_clk();

            self.set_scl_low()?;
            self.set_sda_low()?;
            self.wait_for_clk();
        }

        Ok(())
    }

    /// Read raw bytes from the slave.
    ///
    /// **This is a low-level control function.** For normal I2C devices,
    /// please use the embedded-hal traits [Read], [Write], or
    /// [WriteRead].
    #[inline]
    pub fn raw_read_from_slave(&mut self, input: &mut [u8]) -> Result<(), crate::i2c::Error<E>> {
        for i in 0..input.len() {
            let should_send_ack = i != (input.len() - 1);
            input[i] = self.i2c_read_byte(should_send_ack)?;
        }
        Ok(())
    }

    /// Send raw bytes to the slave.
    ///
    /// **This is a low-level control function.** For normal I2C devices,
    /// please use the embedded-hal traits [Read], [Write], or
    /// [WriteRead].
    #[inline]
    pub fn raw_write_to_slave(&mut self, output: &[u8]) -> Result<(), crate::i2c::Error<E>> {
        for byte in output {
            self.i2c_write_byte(*byte)?;
            self.check_ack()?;
        }
        Ok(())
    }

    #[inline]
    fn set_scl_high(&mut self) -> Result<(), crate::i2c::Error<E>> {
        self.scl.set_high().map_err(Error::Bus)
    }

    #[inline]
    fn set_scl_low(&mut self) -> Result<(), crate::i2c::Error<E>> {
        self.scl.set_low().map_err(Error::Bus)
    }

    #[inline]
    fn set_sda_high(&mut self) -> Result<(), crate::i2c::Error<E>> {
        self.sda.set_high().map_err(Error::Bus)
    }

    #[inline]
    fn set_sda_low(&mut self) -> Result<(), crate::i2c::Error<E>> {
        self.sda.set_low().map_err(Error::Bus)
    }

    #[inline]
    fn wait_for_clk(&mut self) {
        self.delay.delay_ns(self.clock_period_ns);
    }

    #[inline]
    fn check_ack(&mut self) -> Result<(), crate::i2c::Error<E>> {
        if !self.i2c_is_ack()? {
            Err(Error::NoAck)
        } else {
            Ok(())
        }
    }
}

impl<SCL, SDA, DELAY, E> ErrorType for I2cBB<SCL, SDA, DELAY>
where
    SCL: OutputPin<Error = E>,
    SDA: OutputPin<Error = E> + InputPin<Error = E>,
    DELAY: DelayNs,
    E: core::fmt::Debug,
{
    type Error = crate::i2c::Error<E>;
}

impl<SCL, SDA, DELAY, E> I2c<SevenBitAddress> for I2cBB<SCL, SDA, DELAY>
where
    SCL: OutputPin<Error = E>,
    SDA: OutputPin<Error = E> + InputPin<Error = E>,
    DELAY: DelayNs,
    E: core::fmt::Debug,
{
    fn transaction(
        &mut self,
        address: u8,
        operations: &mut [Operation<'_>],
    ) -> Result<(), Self::Error> {
        for operation in operations {
            match operation {
                Operation::Read(buffer) => {
                    // ST
                    self.raw_i2c_start()?;

                    // SAD + R
                    self.i2c_write_byte((address << 1) | 0x1)?;
                    self.check_ack()?;

                    self.raw_read_from_slave(buffer)?;

                    // SP
                    self.raw_i2c_stop()?;
                }
                Operation::Write(bytes) => {
                    // ST
                    self.raw_i2c_start()?;

                    // SAD + W
                    self.i2c_write_byte(address << 1)?;
                    self.check_ack()?;

                    self.raw_write_to_slave(bytes)?;

                    // SP
                    self.raw_i2c_stop()?;
                }
            }
        }
        Ok(())
    }
}

#[cfg(feature = "async")]
impl<SCL, SDA, DELAY, E> I2cBB<SCL, SDA, DELAY>
where
    SCL: OutputPin<Error = E>,
    SDA: OutputPin<Error = E> + InputPin<Error = E>,
    DELAY: AsyncDelayNs + DelayNs,
    E: core::fmt::Debug,
{
    /// Async version of wait_for_timer
    async fn async_wait_for_timer(&mut self) {
        AsyncDelayNs::delay_ns(&mut self.delay, self.clock_period_ns).await;
    }

    /// Async version of raw_i2c_start
    async fn async_raw_i2c_start(&mut self) -> Result<(), crate::i2c::Error<E>> {
        self.sda.set_high().map_err(Error::Bus)?;
        self.async_wait_for_timer().await;
        self.scl.set_high().map_err(Error::Bus)?;
        self.async_wait_for_timer().await;
        self.sda.set_low().map_err(Error::Bus)?;
        self.async_wait_for_timer().await;
        self.scl.set_low().map_err(Error::Bus)?;
        self.async_wait_for_timer().await;
        Ok(())
    }

    /// Async version of raw_i2c_stop
    async fn async_raw_i2c_stop(&mut self) -> Result<(), crate::i2c::Error<E>> {
        self.sda.set_low().map_err(Error::Bus)?;
        self.async_wait_for_timer().await;
        self.scl.set_high().map_err(Error::Bus)?;
        self.async_wait_for_timer().await;
        self.sda.set_high().map_err(Error::Bus)?;
        self.async_wait_for_timer().await;
        Ok(())
    }

    /// Async version of i2c_write_byte
    async fn async_i2c_write_byte(&mut self, byte: u8) -> Result<(), crate::i2c::Error<E>> {
        for bit in (0..8).rev() {
            let bit_value = (byte >> bit) & 1;
            if bit_value == 1 {
                self.sda.set_high().map_err(Error::Bus)?;
            } else {
                self.sda.set_low().map_err(Error::Bus)?;
            }
            self.async_wait_for_timer().await;
            self.scl.set_high().map_err(Error::Bus)?;
            self.async_wait_for_timer().await;
            self.scl.set_low().map_err(Error::Bus)?;
            self.async_wait_for_timer().await;
        }
        Ok(())
    }

    /// Async version of check_ack
    async fn async_check_ack(&mut self) -> Result<(), crate::i2c::Error<E>> {
        self.sda.set_high().map_err(Error::Bus)?;
        self.async_wait_for_timer().await;
        self.scl.set_high().map_err(Error::Bus)?;
        self.async_wait_for_timer().await;

        let ack = self.sda.is_low().map_err(Error::Bus)?;
        if !ack {
            return Err(Error::NoAck);
        }

        self.scl.set_low().map_err(Error::Bus)?;
        self.async_wait_for_timer().await;
        Ok(())
    }

    /// Async version of i2c_read_byte
    async fn async_i2c_read_byte(&mut self, ack: bool) -> Result<u8, crate::i2c::Error<E>> {
        let mut byte = 0u8;
        self.sda.set_high().map_err(Error::Bus)?;

        for bit in (0..8).rev() {
            self.async_wait_for_timer().await;
            self.scl.set_high().map_err(Error::Bus)?;
            self.async_wait_for_timer().await;

            if self.sda.is_high().map_err(Error::Bus)? {
                byte |= 1 << bit;
            }

            self.scl.set_low().map_err(Error::Bus)?;
        }

        // Send ACK/NACK
        if ack {
            self.sda.set_low().map_err(Error::Bus)?;
        } else {
            self.sda.set_high().map_err(Error::Bus)?;
        }
        self.async_wait_for_timer().await;
        self.scl.set_high().map_err(Error::Bus)?;
        self.async_wait_for_timer().await;
        self.scl.set_low().map_err(Error::Bus)?;
        self.async_wait_for_timer().await;

        Ok(byte)
    }

    /// Async version of raw_write_to_slave
    async fn async_raw_write_to_slave(&mut self, bytes: &[u8]) -> Result<(), crate::i2c::Error<E>> {
        for byte in bytes {
            self.async_i2c_write_byte(*byte).await?;
            self.async_check_ack().await?;
        }
        Ok(())
    }

    /// Async version of raw_read_from_slave
    async fn async_raw_read_from_slave(
        &mut self,
        buffer: &mut [u8],
    ) -> Result<(), crate::i2c::Error<E>> {
        let len = buffer.len();
        for (i, byte) in buffer.iter_mut().enumerate() {
            let ack = i < len - 1; // ACK for all bytes except the last one
            *byte = self.async_i2c_read_byte(ack).await?;
        }
        Ok(())
    }
}

#[cfg(feature = "async")]
impl<SCL, SDA, DELAY, E> AsyncI2c<SevenBitAddress> for I2cBB<SCL, SDA, DELAY>
where
    SCL: OutputPin<Error = E>,
    SDA: OutputPin<Error = E> + InputPin<Error = E>,
    DELAY: AsyncDelayNs + DelayNs,
    E: core::fmt::Debug,
{
    async fn transaction(
        &mut self,
        address: u8,
        operations: &mut [AsyncOperation<'_>],
    ) -> Result<(), Self::Error> {
        for operation in operations {
            match operation {
                AsyncOperation::Read(buffer) => {
                    // ST
                    self.async_raw_i2c_start().await?;

                    // SAD + R
                    self.async_i2c_write_byte((address << 1) | 0x1).await?;
                    self.async_check_ack().await?;

                    self.async_raw_read_from_slave(buffer).await?;

                    // SP
                    self.async_raw_i2c_stop().await?;
                }
                AsyncOperation::Write(bytes) => {
                    // ST
                    self.async_raw_i2c_start().await?;

                    // SAD + W
                    self.async_i2c_write_byte(address << 1).await?;
                    self.async_check_ack().await?;

                    self.async_raw_write_to_slave(bytes).await?;

                    // SP
                    self.async_raw_i2c_stop().await?;
                }
            }
        }
        Ok(())
    }
}
