//! Serial Peripheral Interface
//!
//! This implementation consumes the following hardware resources:
//! - Periodic timer to mark clock cycles
//! - Output GPIO pin for clock signal (SCLK)
//! - Output GPIO pin for data transmission (Master Output Slave Input - MOSI)
//! - Input GPIO pin for data reception (Master Input Slave Output - MISO)
//!
//! The timer must be configured to twice the desired communication frequency.
//!
//! SS/CS (slave select) must be handled independently.
//!
//! MSB-first and LSB-first bit orders are supported.
//!

pub use embedded_hal::spi::{MODE_0, MODE_1, MODE_2, MODE_3};

use embedded_hal::delay::DelayNs;
use embedded_hal::digital::{InputPin, OutputPin};
use embedded_hal::spi::{Mode, Operation, Polarity, SpiBus, SpiDevice as SpiDeviceTrait};
use embedded_hal_nb::spi::{ErrorType, FullDuplex};

#[cfg(feature = "async")]
use embedded_hal_async::delay::DelayNs as AsyncDelayNs;
#[cfg(feature = "async")]
use embedded_hal_async::spi::{
    Operation as AsyncOperation, SpiBus as AsyncSpiBus, SpiDevice as AsyncSpiDeviceTrait,
};

/// Error type
#[derive(Debug)]
pub enum Error<E> {
    /// Communication error
    Bus(E),
    /// Attempted read without input data
    NoData,
}

impl<E: core::fmt::Debug> embedded_hal::spi::Error for Error<E> {
    fn kind(&self) -> embedded_hal::spi::ErrorKind {
        match self {
            Error::Bus(_) => embedded_hal::spi::ErrorKind::Other,
            Error::NoData => embedded_hal::spi::ErrorKind::Other,
        }
    }
}

/// Transmission bit order
#[derive(Debug)]
pub enum BitOrder {
    /// Most significant bit first
    MSBFirst,
    /// Least significant bit first
    #[allow(dead_code)]
    LSBFirst,
}

impl Default for BitOrder {
    /// Default bit order: MSB first
    fn default() -> Self {
        BitOrder::MSBFirst
    }
}

/// A Full-Duplex SPI implementation, takes 3 pins, and a delay for timing.
pub struct SPI<Miso, Mosi, Sck, DELAY>
where
    Miso: InputPin,
    Mosi: OutputPin,
    Sck: OutputPin,
    DELAY: DelayNs,
{
    mode: Mode,
    miso: Miso,
    mosi: Mosi,
    sck: Sck,
    delay: DELAY,
    read_val: Option<u8>,
    bit_order: BitOrder,
    clock_period_ns: u32,
}

impl<Miso, Mosi, Sck, DELAY, E> SPI<Miso, Mosi, Sck, DELAY>
where
    Miso: InputPin<Error = E>,
    Mosi: OutputPin<Error = E>,
    Sck: OutputPin<Error = E>,
    DELAY: DelayNs,
    E: core::fmt::Debug,
{
    /// Create instance
    pub fn new(
        mode: Mode,
        miso: Miso,
        mosi: Mosi,
        sck: Sck,
        delay: DELAY,
        clock_frequency_hz: u32,
    ) -> Self {
        let clock_period_ns = 1_000_000_000 / (clock_frequency_hz * 2); // Half period for each edge
        let mut spi = SPI {
            mode,
            miso,
            mosi,
            sck,
            delay,
            read_val: None,
            bit_order: BitOrder::default(),
            clock_period_ns,
        };

        match mode.polarity {
            Polarity::IdleLow => spi.sck.set_low(),
            Polarity::IdleHigh => spi.sck.set_high(),
        }
        .unwrap_or(());

        spi
    }

    /// Set transmission bit order
    #[allow(dead_code)]
    pub fn set_bit_order(&mut self, order: BitOrder) {
        self.bit_order = order;
    }

    /// Update the clock frequency
    #[allow(dead_code)]
    pub fn set_clock_frequency(&mut self, clock_frequency_hz: u32) {
        self.clock_period_ns = 1_000_000_000 / (clock_frequency_hz * 2);
    }

    fn read_bit(&mut self) -> nb::Result<(), crate::spi::Error<E>> {
        let is_miso_high = self.miso.is_high().map_err(Error::Bus)?;
        let shifted_value = self.read_val.unwrap_or(0) << 1;
        if is_miso_high {
            self.read_val = Some(shifted_value | 1);
        } else {
            self.read_val = Some(shifted_value);
        }
        Ok(())
    }

    #[inline]
    fn set_clk_high(&mut self) -> Result<(), crate::spi::Error<E>> {
        self.sck.set_high().map_err(Error::Bus)
    }

    #[inline]
    fn set_clk_low(&mut self) -> Result<(), crate::spi::Error<E>> {
        self.sck.set_low().map_err(Error::Bus)
    }

    #[inline]
    fn wait_for_timer(&mut self) {
        self.delay.delay_ns(self.clock_period_ns);
    }

    /// Transfer a single byte and store the read result
    fn transfer_byte(&mut self, byte: u8) -> Result<(), crate::spi::Error<E>> {
        self.read_val = Some(0); // Initialize read value

        for bit_offset in 0..8 {
            let out_bit = match self.bit_order {
                BitOrder::MSBFirst => (byte >> (7 - bit_offset)) & 0b1,
                BitOrder::LSBFirst => (byte >> bit_offset) & 0b1,
            };

            if out_bit == 1 {
                self.mosi.set_high().map_err(Error::Bus)?;
            } else {
                self.mosi.set_low().map_err(Error::Bus)?;
            }

            match self.mode {
                MODE_0 => {
                    self.wait_for_timer();
                    self.set_clk_high()?;
                    self.read_bit().map_err(|e| match e {
                        nb::Error::Other(err) => err,
                        nb::Error::WouldBlock => Error::NoData,
                    })?;
                    self.wait_for_timer();
                    self.set_clk_low()?;
                }
                MODE_1 => {
                    self.set_clk_high()?;
                    self.wait_for_timer();
                    self.read_bit().map_err(|e| match e {
                        nb::Error::Other(err) => err,
                        nb::Error::WouldBlock => Error::NoData,
                    })?;
                    self.set_clk_low()?;
                    self.wait_for_timer();
                }
                MODE_2 => {
                    self.wait_for_timer();
                    self.set_clk_low()?;
                    self.read_bit().map_err(|e| match e {
                        nb::Error::Other(err) => err,
                        nb::Error::WouldBlock => Error::NoData,
                    })?;
                    self.wait_for_timer();
                    self.set_clk_high()?;
                }
                MODE_3 => {
                    self.set_clk_low()?;
                    self.wait_for_timer();
                    self.read_bit().map_err(|e| match e {
                        nb::Error::Other(err) => err,
                        nb::Error::WouldBlock => Error::NoData,
                    })?;
                    self.set_clk_high()?;
                    self.wait_for_timer();
                }
            }
        }

        Ok(())
    }
}

impl<Miso, Mosi, Sck, DELAY, E> ErrorType for SPI<Miso, Mosi, Sck, DELAY>
where
    Miso: InputPin<Error = E>,
    Mosi: OutputPin<Error = E>,
    Sck: OutputPin<Error = E>,
    DELAY: DelayNs,
    E: core::fmt::Debug,
{
    type Error = crate::spi::Error<E>;
}

impl<Miso, Mosi, Sck, DELAY, E> FullDuplex<u8> for SPI<Miso, Mosi, Sck, DELAY>
where
    Miso: InputPin<Error = E>,
    Mosi: OutputPin<Error = E>,
    Sck: OutputPin<Error = E>,
    DELAY: DelayNs,
    E: core::fmt::Debug,
{
    #[inline]
    fn read(&mut self) -> nb::Result<u8, Self::Error> {
        match self.read_val {
            Some(val) => Ok(val),
            None => Err(nb::Error::Other(crate::spi::Error::NoData)),
        }
    }

    fn write(&mut self, byte: u8) -> nb::Result<(), Self::Error> {
        for bit_offset in 0..8 {
            let out_bit = match self.bit_order {
                BitOrder::MSBFirst => (byte >> (7 - bit_offset)) & 0b1,
                BitOrder::LSBFirst => (byte >> bit_offset) & 0b1,
            };

            if out_bit == 1 {
                self.mosi.set_high().map_err(Error::Bus)?;
            } else {
                self.mosi.set_low().map_err(Error::Bus)?;
            }

            match self.mode {
                MODE_0 => {
                    self.wait_for_timer();
                    self.set_clk_high()?;
                    self.read_bit()?;
                    self.wait_for_timer();
                    self.set_clk_low()?;
                }
                MODE_1 => {
                    self.set_clk_high()?;
                    self.wait_for_timer();
                    self.read_bit()?;
                    self.set_clk_low()?;
                    self.wait_for_timer();
                }
                MODE_2 => {
                    self.wait_for_timer();
                    self.set_clk_low()?;
                    self.read_bit()?;
                    self.wait_for_timer();
                    self.set_clk_high()?;
                }
                MODE_3 => {
                    self.set_clk_low()?;
                    self.wait_for_timer();
                    self.read_bit()?;
                    self.set_clk_high()?;
                    self.wait_for_timer();
                }
            }
        }

        Ok(())
    }
}

impl<Miso, Mosi, Sck, DELAY, E> SpiBus for SPI<Miso, Mosi, Sck, DELAY>
where
    Miso: InputPin<Error = E>,
    Mosi: OutputPin<Error = E>,
    Sck: OutputPin<Error = E>,
    DELAY: DelayNs,
    E: core::fmt::Debug,
{
    fn read(&mut self, words: &mut [u8]) -> Result<(), Self::Error> {
        for word in words.iter_mut() {
            // Send dummy byte (0x00) and read response
            self.transfer_byte(0x00)?;
            *word = self.read_val.take().ok_or(Error::NoData)?;
        }
        Ok(())
    }

    fn write(&mut self, words: &[u8]) -> Result<(), Self::Error> {
        for &word in words {
            self.transfer_byte(word)?;
            // Discard read data
            self.read_val = None;
        }
        Ok(())
    }

    fn transfer(&mut self, read: &mut [u8], write: &[u8]) -> Result<(), Self::Error> {
        let max_len = read.len().max(write.len());

        for i in 0..max_len {
            let write_byte = write.get(i).copied().unwrap_or(0x00);
            self.transfer_byte(write_byte)?;

            if let Some(read_byte) = read.get_mut(i) {
                *read_byte = self.read_val.take().ok_or(Error::NoData)?;
            } else {
                // Discard read data if read buffer is shorter
                self.read_val = None;
            }
        }
        Ok(())
    }

    fn transfer_in_place(&mut self, words: &mut [u8]) -> Result<(), Self::Error> {
        for word in words.iter_mut() {
            self.transfer_byte(*word)?;
            *word = self.read_val.take().ok_or(Error::NoData)?;
        }
        Ok(())
    }

    fn flush(&mut self) -> Result<(), Self::Error> {
        // For bit-bang implementation, operations are synchronous
        // so no flushing is needed
        Ok(())
    }
}

/// SPI device with chip select pin
pub struct SpiDevice<SPI, CS> {
    bus: SPI,
    cs: CS,
}

impl<SPI, CS> SpiDevice<SPI, CS> {
    /// Create a new SPI device with the given bus and chip select pin
    pub fn new(bus: SPI, cs: CS) -> Self {
        Self { bus, cs }
    }
}

impl<SPI, CS, E> embedded_hal::spi::ErrorType for SpiDevice<SPI, CS>
where
    SPI: embedded_hal::spi::ErrorType<Error = crate::spi::Error<E>>,
    CS: OutputPin,
    E: core::fmt::Debug,
{
    type Error = crate::spi::Error<E>;
}

impl<SPI, CS, E> SpiDeviceTrait<u8> for SpiDevice<SPI, CS>
where
    SPI: SpiBus<u8, Error = crate::spi::Error<E>>,
    CS: OutputPin<Error = E>,
    E: core::fmt::Debug,
{
    fn transaction(&mut self, operations: &mut [Operation<'_, u8>]) -> Result<(), Self::Error> {
        // Assert CS (active low)
        self.cs.set_low().map_err(Error::Bus)?;

        let result = self.perform_operations(operations);

        // Deassert CS
        let cs_result = self.cs.set_high().map_err(Error::Bus);

        // Flush the bus
        let flush_result = self.bus.flush();

        // Return the first error encountered, prioritizing bus errors
        result?;
        flush_result?;
        cs_result?;

        Ok(())
    }
}

impl<SPI, CS, E> SpiDevice<SPI, CS>
where
    SPI: SpiBus<u8, Error = crate::spi::Error<E>>,
    CS: OutputPin<Error = E>,
    E: core::fmt::Debug,
{
    fn perform_operations(
        &mut self,
        operations: &mut [Operation<'_, u8>],
    ) -> Result<(), crate::spi::Error<E>> {
        for operation in operations {
            match operation {
                Operation::Read(buf) => {
                    self.bus.read(buf)?;
                }
                Operation::Write(buf) => {
                    self.bus.write(buf)?;
                }
                Operation::Transfer(read, write) => {
                    self.bus.transfer(read, write)?;
                }
                Operation::TransferInPlace(buf) => {
                    self.bus.transfer_in_place(buf)?;
                }
                Operation::DelayNs(ns) => {
                    // For bit-bang implementation, we don't have a way to delay
                    // This should be handled by the user's delay implementation
                    // For now, we'll ignore this operation
                    let _ = ns;
                }
            }
        }
        Ok(())
    }
}

#[cfg(feature = "async")]
impl<Miso, Mosi, Sck, DELAY, E> SPI<Miso, Mosi, Sck, DELAY>
where
    Miso: InputPin<Error = E>,
    Mosi: OutputPin<Error = E>,
    Sck: OutputPin<Error = E>,
    DELAY: AsyncDelayNs + DelayNs,
    E: core::fmt::Debug,
{
    /// Async version of wait_for_timer
    async fn async_wait_for_timer(&mut self) {
        AsyncDelayNs::delay_ns(&mut self.delay, self.clock_period_ns).await;
    }

    /// Async version of transfer_byte
    async fn async_transfer_byte(&mut self, byte: u8) -> Result<(), crate::spi::Error<E>> {
        self.read_val = Some(0); // Initialize read value

        for bit_offset in 0..8 {
            let out_bit = match self.bit_order {
                BitOrder::MSBFirst => (byte >> (7 - bit_offset)) & 0b1,
                BitOrder::LSBFirst => (byte >> bit_offset) & 0b1,
            };

            if out_bit == 1 {
                self.mosi.set_high().map_err(Error::Bus)?;
            } else {
                self.mosi.set_low().map_err(Error::Bus)?;
            }

            match self.mode {
                MODE_0 => {
                    self.async_wait_for_timer().await;
                    self.set_clk_high()?;
                    self.async_read_bit().await?;
                    self.async_wait_for_timer().await;
                    self.set_clk_low()?;
                }
                MODE_1 => {
                    self.set_clk_high()?;
                    self.async_wait_for_timer().await;
                    self.async_read_bit().await?;
                    self.set_clk_low()?;
                    self.async_wait_for_timer().await;
                }
                MODE_2 => {
                    self.async_wait_for_timer().await;
                    self.set_clk_low()?;
                    self.async_read_bit().await?;
                    self.async_wait_for_timer().await;
                    self.set_clk_high()?;
                }
                MODE_3 => {
                    self.set_clk_low()?;
                    self.async_wait_for_timer().await;
                    self.async_read_bit().await?;
                    self.set_clk_high()?;
                    self.async_wait_for_timer().await;
                }
            }
        }

        Ok(())
    }

    /// Async version of read_bit
    async fn async_read_bit(&mut self) -> Result<(), crate::spi::Error<E>> {
        let is_miso_high = self.miso.is_high().map_err(Error::Bus)?;
        let shifted_value = self.read_val.unwrap_or(0) << 1;
        if is_miso_high {
            self.read_val = Some(shifted_value | 1);
        } else {
            self.read_val = Some(shifted_value);
        }
        Ok(())
    }
}

#[cfg(feature = "async")]
impl<Miso, Mosi, Sck, DELAY, E> AsyncSpiBus for SPI<Miso, Mosi, Sck, DELAY>
where
    Miso: InputPin<Error = E>,
    Mosi: OutputPin<Error = E>,
    Sck: OutputPin<Error = E>,
    DELAY: AsyncDelayNs + DelayNs,
    E: core::fmt::Debug,
{
    async fn read(&mut self, words: &mut [u8]) -> Result<(), Self::Error> {
        for word in words.iter_mut() {
            // Send dummy byte (0x00) and read response
            self.async_transfer_byte(0x00).await?;
            *word = self.read_val.take().ok_or(Error::NoData)?;
        }
        Ok(())
    }

    async fn write(&mut self, words: &[u8]) -> Result<(), Self::Error> {
        for &word in words {
            self.async_transfer_byte(word).await?;
            // Discard read data
            self.read_val = None;
        }
        Ok(())
    }

    async fn transfer(&mut self, read: &mut [u8], write: &[u8]) -> Result<(), Self::Error> {
        let max_len = read.len().max(write.len());

        for i in 0..max_len {
            let write_byte = write.get(i).copied().unwrap_or(0x00);
            self.async_transfer_byte(write_byte).await?;

            if let Some(read_byte) = read.get_mut(i) {
                *read_byte = self.read_val.take().ok_or(Error::NoData)?;
            } else {
                // Discard read data if read buffer is shorter
                self.read_val = None;
            }
        }
        Ok(())
    }

    async fn transfer_in_place(&mut self, words: &mut [u8]) -> Result<(), Self::Error> {
        for word in words.iter_mut() {
            self.async_transfer_byte(*word).await?;
            *word = self.read_val.take().ok_or(Error::NoData)?;
        }
        Ok(())
    }

    async fn flush(&mut self) -> Result<(), Self::Error> {
        // For bit-bang implementation, operations are synchronous
        // so no flushing is needed
        Ok(())
    }
}

#[cfg(feature = "async")]
impl<SPI, CS, E> AsyncSpiDeviceTrait for SpiDevice<SPI, CS>
where
    SPI: AsyncSpiBus<Error = crate::spi::Error<E>>,
    CS: OutputPin<Error = E>,
    E: core::fmt::Debug,
{
    async fn transaction(
        &mut self,
        operations: &mut [AsyncOperation<'_, u8>],
    ) -> Result<(), Self::Error> {
        // Assert CS (active low)
        self.cs.set_low().map_err(Error::Bus)?;

        let result = self.async_perform_operations(operations).await;

        // Deassert CS
        let cs_result = self.cs.set_high().map_err(Error::Bus);

        // Flush the bus
        let flush_result = self.bus.flush().await;

        // Return the first error encountered, prioritizing bus errors
        result?;
        flush_result?;
        cs_result?;

        Ok(())
    }
}

#[cfg(feature = "async")]
impl<SPI, CS, E> SpiDevice<SPI, CS>
where
    SPI: AsyncSpiBus<Error = crate::spi::Error<E>>,
    CS: OutputPin<Error = E>,
    E: core::fmt::Debug,
{
    async fn async_perform_operations(
        &mut self,
        operations: &mut [AsyncOperation<'_, u8>],
    ) -> Result<(), crate::spi::Error<E>> {
        for operation in operations {
            match operation {
                AsyncOperation::Read(buf) => {
                    self.bus.read(buf).await?;
                }
                AsyncOperation::Write(buf) => {
                    self.bus.write(buf).await?;
                }
                AsyncOperation::Transfer(read, write) => {
                    self.bus.transfer(read, write).await?;
                }
                AsyncOperation::TransferInPlace(buf) => {
                    self.bus.transfer_in_place(buf).await?;
                }
                AsyncOperation::DelayNs(ns) => {
                    // For bit-bang implementation, we don't have a way to delay
                    // This should be handled by the user's delay implementation
                    // For now, we'll ignore this operation
                    let _ = ns;
                }
            }
        }
        Ok(())
    }
}
