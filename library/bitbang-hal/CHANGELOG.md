# Changelog

## [Unreleased] - embedded-hal 1.0 Update

### Added

- Support for embedded-hal 1.0
- Support for embedded-hal-nb 1.0 for non-blocking serial and SPI traits
- SpiBus trait implementation for bus-level SPI operations
- SpiDevice struct and trait implementation for device-level SPI operations with CS pin management
- **Async support**: Optional "async" feature providing full async/await support
  - `embedded_hal_async::i2c::I2c` for async I2C operations
  - `embedded_hal_async::spi::SpiBus` and `embedded_hal_async::spi::SpiDevice` for async SPI operations
  - `embedded_io_async::Read` and `embedded_io_async::Write` for async serial operations
- Delay-based timing using `DelayNs` trait
- Frequency/baud rate configuration in constructors
- Comprehensive migration guide (MIGRATION.md)
- Embassy-stm32 compatibility example
- Better error handling with proper embedded-hal error trait implementations

### Changed

- **BREAKING**: Updated all constructors to use `DelayNs` instead of timer-based approach
- **BREAKING**: I2C now implements unified `I2c<SevenBitAddress>` trait instead of separate Read/Write/WriteRead traits
- **BREAKING**: <PERSON><PERSON> now uses `embedded-hal-nb` traits instead of `embedded-hal` blocking traits
- **BREAKING**: SPI now uses `embedded-hal-nb::spi::FullDuplex` trait
- **BREAKING**: SPI `send()` method renamed to `write()` to match new trait
- **BREAKING**: All constructors now require frequency/baud rate parameters
- Error types now implement appropriate embedded-hal error traits
- Updated documentation with embassy-stm32 examples

### Removed

- **BREAKING**: Timer-based timing approach (replaced with delay-based)
- **BREAKING**: SPI `access_timer()` method (replaced with `set_clock_frequency()`)
- **BREAKING**: Blocking SPI trait implementations
- Unused `embedded-time` dependency

### Dependencies

- Added optional `embedded-hal-async` dependency for async I2C and SPI support
- Added optional `embedded-io-async` dependency for async serial support

### Migration

- See [MIGRATION.md](MIGRATION.md) for detailed migration instructions
- All HALs that implement embedded-hal 1.0 traits are now supported
- Embassy-stm32, stm32f1xx-hal, and other modern HALs are fully compatible

### Compatibility

- ✅ embassy-stm32
- ✅ embassy-time (for DelayNs implementation)
- ✅ Any HAL implementing embedded-hal 1.0 traits
- ✅ Any DelayNs implementation
- ✅ Any AsyncDelayNs implementation (for async feature)

### Technical Details

- Clock timing is now specified in Hz during construction
- Delay periods are calculated automatically based on desired frequencies
- GPIO error types must implement `Debug` trait (satisfied by most HALs including embassy-stm32's `Infallible`)
- All trait implementations are generic over GPIO and delay types for maximum compatibility
