# `embedded-hal` traits implementation by bit banging

[![crates.io](https://img.shields.io/crates/v/bitbang-hal.svg)](https://crates.io/crates/bitbang-hal)
[![Docs](https://docs.rs/bitbang-hal/badge.svg)](https://docs.rs/bitbang-hal)

This is a [bit banging] implementation of the [`embedded-hal`] traits.

[bit banging]: https://en.wikipedia.org/wiki/Bit_banging
[`embedded-hal`]: https://github.com/rust-embedded/embedded-hal

## Features

- **embedded-hal 1.0 support**: Updated to work with the latest embedded-hal version
- **HAL agnostic**: Works with any HAL that implements embedded-hal traits (embassy-stm32, stm32f1xx-hal, etc.)
- **Delay-based timing**: Uses `DelayNs` trait for precise timing control
- **I2C, SPI, and Serial**: Bit-bang implementations for common communication protocols
- **Modern SPI traits**: Implements both `SpiBus` and `SpiDevice` for flexible SPI usage patterns
- **Async support**: Full async/await support with `embedded-hal-async` and `embedded-io-async` (optional feature)

## Usage

See the [Migration Guide](MIGRATION.md) for updating from older versions.

### Quick Example with embassy-stm32

```rust
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_time::Delay;
use embedded_hal::i2c::I2c;

// Initialize embassy-stm32
let p = embassy_stm32::init(Default::default());

// Configure GPIO pins
let scl = Output::new(p.PA8, Level::High, Speed::Medium);
let sda = Output::new(p.PA9, Level::High, Speed::Medium);

// Create bitbang I2C instance
let mut i2c = bitbang_hal::i2c::I2cBB::new(scl, sda, Delay, 100_000);

// Use I2C
i2c.write(0x48, &[0x01, 0x02]).unwrap();
```

### SPI Example

```rust
use embassy_stm32::gpio::{Level, Output, Input, Pull, Speed};
use embassy_time::Delay;
use embedded_hal::spi::{SpiBus, SpiDevice};

// Configure GPIO pins
let miso = Input::new(p.PA6, Pull::None);
let mosi = Output::new(p.PA7, Level::Low, Speed::Medium);
let sck = Output::new(p.PA5, Level::Low, Speed::Medium);
let cs = Output::new(p.PA4, Level::High, Speed::Medium);

// Option 1: Use SpiBus directly (exclusive access)
let mut spi_bus = bitbang_hal::spi::SPI::new(
    bitbang_hal::spi::MODE_0, miso, mosi, sck, Delay, 1_000_000
);
spi_bus.write(&[0x01, 0x02]).unwrap();

// Option 2: Use SpiDevice (with CS management)
let mut spi_device = bitbang_hal::spi::SpiDevice::new(spi_bus, cs);
spi_device.write(&[0x01, 0x02]).unwrap(); // CS is managed automatically
```

### Async Example (requires "async" feature)

```rust
use embassy_stm32::gpio::{Level, Output, Input, Pull, Speed};
use embassy_time::Delay;
use embedded_hal_async::i2c::I2c as AsyncI2c;

// Enable async feature in Cargo.toml:
// bitbang-hal = { version = "0.3", features = ["async"] }

// Configure GPIO pins
let scl = Output::new(p.PA8, Level::High, Speed::Medium);
let sda = Output::new(p.PA9, Level::High, Speed::Medium);

// Create async I2C instance
let mut i2c = bitbang_hal::i2c::I2cBB::new(scl, sda, Delay, 100_000);

// Use async methods
i2c.write(0x48, &[0x01, 0x02]).await.unwrap();
let mut buffer = [0u8; 2];
i2c.read(0x48, &mut buffer).await.unwrap();
```

See example programs in the `examples` folder.

## Support

For questions, issues, feature requests, and other changes, please file an
issue in the github project.

## License

Licensed under MIT license ([LICENSE-MIT](LICENSE-MIT) or http://opensource.org/licenses/MIT)

### Contributing

Unless you explicitly state otherwise, any contribution intentionally submitted
for inclusion in the work by you shall be licensed as above, without any
additional terms or conditions.
