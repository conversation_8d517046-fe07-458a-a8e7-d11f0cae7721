[package]
name = "bitbang-hal"
version = "0.3.3"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
description = "Implements embedded-hal traits by bitbanging"
license = "MIT"
repository = "https://github.com/sajattack/bitbang-hal"
readme = "README.md"
keywords = ["no_std", "embedded", "bitbang", "embedded-hal", "hal"]
categories = ["embedded", "no-std"]

[dependencies]
nb = "1.1.0"
embedded-hal = "1.0.0"
embedded-hal-nb = "1.0.0"
embedded-hal-async = { version = "1.0.0", optional = true }
embedded-io-async = { version = "0.6.1", optional = true }

[features]
default = ["async"]
async = ["embedded-hal-async", "embedded-io-async"]

[lib]
test = false
bench = false

