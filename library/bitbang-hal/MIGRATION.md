# Migration Guide: embedded-hal 1.0 Update

This document describes the changes made to update bitbang-hal to work with embedded-hal 1.0.

## Breaking Changes

### 1. Constructor Changes

All constructors now require a delay implementation and frequency/baud rate parameter instead of a timer:

#### I2C

```rust
// Old (embedded-hal 0.2)
let i2c = I2cBB::new(scl, sda, timer);

// New (embedded-hal 1.0)
let delay = /* your DelayNs implementation */;
let i2c = I2cBB::new(scl, sda, delay, 100_000); // 100kHz clock frequency
```

#### Serial

```rust
// Old (embedded-hal 0.2)
let serial = Serial::new(tx, rx, timer);

// New (embedded-hal 1.0)
let delay = /* your DelayNs implementation */;
let serial = Serial::new(tx, rx, delay, 9600); // 9600 baud rate
```

#### SPI

```rust
// Old (embedded-hal 0.2)
let spi = SPI::new(mode, miso, mosi, sck, timer);

// New (embedded-hal 1.0) - SpiBus for exclusive bus access
let delay = /* your DelayNs implementation */;
let spi_bus = SPI::new(mode, miso, mosi, sck, delay, 1_000_000); // 1MHz clock frequency

// New (embedded-hal 1.0) - SpiDevice for shared bus with CS pin
let cs_pin = /* your OutputPin implementation */;
let spi_device = bitbang_hal::spi::SpiDevice::new(spi_bus, cs_pin);
```

### 2. Trait Changes

#### I2C

- Now implements `embedded_hal::i2c::I2c<SevenBitAddress>` instead of separate `Read`, `Write`, `WriteRead` traits
- All I2C operations are now available through the unified `I2c` trait
- The trait provides default implementations for `read()`, `write()`, and `write_read()` methods

#### Serial

- Now implements `embedded_hal_nb::serial::Read` and `embedded_hal_nb::serial::Write` traits
- These are in the `embedded-hal-nb` crate, not the main `embedded-hal` crate

#### SPI

- Now implements `embedded_hal::spi::SpiBus` trait for bus-level operations
- Added `SpiDevice` struct that implements `embedded_hal::spi::SpiDevice` trait for device-level operations with CS pin management
- Still implements `embedded_hal_nb::spi::FullDuplex` trait for compatibility
- The `send()` method has been renamed to `write()`
- Blocking SPI traits have been removed

### 3. Timer Replacement

The timer-based approach has been replaced with a delay-based approach:

- Instead of using `CountDown + Periodic` timer traits, the library now uses `DelayNs`
- Clock frequencies are specified during construction
- The `access_timer()` method in SPI has been replaced with `set_clock_frequency()`

### 4. Error Types

All error types now implement the appropriate embedded-hal error traits:

- `i2c::Error` implements `embedded_hal::i2c::Error`
- `serial::Error` implements `embedded_hal_nb::serial::Error`
- `spi::Error` implements `embedded_hal_nb::spi::Error`

### 5. Dependencies

The following dependencies have been added:

- `embedded-hal-nb = "1.0.0"` - for non-blocking serial and SPI traits

## Example Usage with embassy-time

```rust
use embassy_time::Delay;
use embedded_hal::i2c::I2c;

// Create a delay implementation
let delay = Delay;

// Create I2C instance
let mut i2c = bitbang_hal::i2c::I2cBB::new(scl_pin, sda_pin, delay, 100_000);

// Use I2C (same as before)
i2c.write(0x48, &[0x01, 0x02]).unwrap();
let mut buffer = [0u8; 2];
i2c.read(0x48, &mut buffer).unwrap();
```

## Example Usage with embassy-stm32

### I2C Example

```rust
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_time::Delay;
use embedded_hal::i2c::I2c;

// Initialize embassy-stm32
let p = embassy_stm32::init(Default::default());

// Configure GPIO pins for I2C
let scl = Output::new(p.PA8, Level::High, Speed::Medium);
let sda = Output::new(p.PA9, Level::High, Speed::Medium);

// Create bitbang I2C instance
let mut i2c = bitbang_hal::i2c::I2cBB::new(scl, sda, Delay, 100_000);

// Use I2C
i2c.write(0x48, &[0x01, 0x02]).unwrap();
let mut buffer = [0u8; 2];
i2c.read(0x48, &mut buffer).unwrap();
```

### SPI Examples

#### Using SpiBus (exclusive bus access)

```rust
use embassy_stm32::gpio::{Level, Output, Input, Pull, Speed};
use embassy_time::Delay;
use embedded_hal::spi::SpiBus;

// Configure GPIO pins for SPI
let miso = Input::new(p.PA6, Pull::None);
let mosi = Output::new(p.PA7, Level::Low, Speed::Medium);
let sck = Output::new(p.PA5, Level::Low, Speed::Medium);

// Create bitbang SPI bus
let mut spi_bus = bitbang_hal::spi::SPI::new(
    bitbang_hal::spi::MODE_0,
    miso, mosi, sck, Delay, 1_000_000
);

// Use SpiBus directly
spi_bus.write(&[0x01, 0x02]).unwrap();
let mut buffer = [0u8; 2];
spi_bus.read(&mut buffer).unwrap();
```

#### Using SpiDevice (shared bus with CS management)

```rust
use embassy_stm32::gpio::{Level, Output, Input, Pull, Speed};
use embassy_time::Delay;
use embedded_hal::spi::SpiDevice;

// Configure GPIO pins
let miso = Input::new(p.PA6, Pull::None);
let mosi = Output::new(p.PA7, Level::Low, Speed::Medium);
let sck = Output::new(p.PA5, Level::Low, Speed::Medium);
let cs = Output::new(p.PA4, Level::High, Speed::Medium); // CS pin

// Create SPI bus
let spi_bus = bitbang_hal::spi::SPI::new(
    bitbang_hal::spi::MODE_0,
    miso, mosi, sck, Delay, 1_000_000
);

// Create SPI device with CS pin
let mut spi_device = bitbang_hal::spi::SpiDevice::new(spi_bus, cs);

// Use SpiDevice (CS is managed automatically)
spi_device.write(&[0x01, 0x02]).unwrap();
let mut buffer = [0u8; 2];
spi_device.read(&mut buffer).unwrap();
```

Note: For I2C, you typically want to use open-drain outputs. Check embassy-stm32 documentation for the correct GPIO configuration for your specific use case.

## Async Support (Optional Feature)

bitbang-hal now supports async/await with the optional "async" feature:

```toml
[dependencies]
bitbang-hal = { version = "0.3", features = ["async"] }
```

### Async Traits Implemented

- **I2C**: `embedded_hal_async::i2c::I2c`
- **SPI**: `embedded_hal_async::spi::SpiBus` and `embedded_hal_async::spi::SpiDevice`
- **Serial**: `embedded_io_async::Read` and `embedded_io_async::Write`

### Async Usage Example

```rust
use embassy_time::Delay;
use embedded_hal_async::i2c::I2c as AsyncI2c;

// Create async I2C instance
let mut i2c = bitbang_hal::i2c::I2cBB::new(scl, sda, Delay, 100_000);

// Use async methods
i2c.write(0x48, &[0x01, 0x02]).await?;
let mut buffer = [0u8; 2];
i2c.read(0x48, &mut buffer).await?;
```

### Requirements for Async

- Your delay implementation must implement both `embedded_hal::delay::DelayNs` and `embedded_hal_async::delay::DelayNs`
- `embassy_time::Delay` satisfies both requirements
- All GPIO pins must implement the standard embedded-hal traits

### Async vs Sync

- **Sync version**: Uses blocking delays, suitable for simple applications
- **Async version**: Uses async delays, suitable for concurrent applications with Embassy or other async runtimes
- Both versions can coexist in the same application (with different instances)

## Compatibility

This update is a breaking change. Code using the old API will need to be updated to use the new constructors and potentially different trait imports.

The examples in the `examples/` directory will need to be updated to work with the new API.
