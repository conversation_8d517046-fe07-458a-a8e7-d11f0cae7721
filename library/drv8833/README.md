# DRV8833 Motor Driver for Embassy

这是一个兼容 Embassy 异步框架的 DRV8833 双 H 桥电机驱动库。DRV8833 是一个低压双 H 桥电机驱动 IC，可以控制两个直流电机或一个步进电机。

## 特性

- 🚀 完全兼容 Embassy 异步框架
- 🔧 支持双电机独立控制
- ⚡ PWM 速度控制
- 🎯 方向控制（前进、后退、刹车、滑行）
- 🛡️ 类型安全的错误处理
- 📦 `no_std`支持

## 硬件连接

### DRV8833 引脚说明

- **AIN1, AIN2**: 电机 A 控制引脚
- **BIN1, BIN2**: 电机 B 控制引脚
- **AOUT1, AOUT2**: 电机 A 输出
- **BOUT1, BOUT2**: 电机 B 输出
- **VCC**: 逻辑电源 (2.7V - 10.8V)
- **VM**: 电机电源 (2.7V - 10.8V)
- **GND**: 接地

### 典型连接示例 (STM32F103)

```
STM32F103    DRV8833
---------    -------
PA0      ->  AIN1
PA1      ->  AIN2
PA2      ->  BIN1
PA3      ->  BIN2
PA6      ->  PWM A (可选，用于速度控制)
PA7      ->  PWM B (可选，用于速度控制)
3.3V     ->  VCC
GND      ->  GND
```

## 使用方法

### 添加依赖

在你的`Cargo.toml`中添加：

```toml
[dependencies]
drv8833 = { path = "path/to/drv8833" }
embassy-stm32 = { version = "0.1", features = ["stm32f103c8"] }
embedded-hal = "1.0"
```

### 双电机控制示例 (使用 SimplePwmChannel)

```rust
use drv8833::{Drv8833, Motor, Direction};
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::time::Hertz;

// 初始化GPIO引脚
let ain1 = Output::new(p.PA0, Level::Low, Speed::Low);
let ain2 = Output::new(p.PA1, Level::Low, Speed::Low);
let bin1 = Output::new(p.PA2, Level::Low, Speed::Low);
let bin2 = Output::new(p.PA3, Level::Low, Speed::Low);

// 初始化PWM
let ch1 = PwmPin::new_ch1(p.PA6, embassy_stm32::gpio::OutputType::PushPull);
let ch2 = PwmPin::new_ch2(p.PA7, embassy_stm32::gpio::OutputType::PushPull);
let mut pwm = SimplePwm::new(p.TIM3, Some(ch1), Some(ch2), None, None, Hertz(10_000), Default::default());
pwm.enable(timer::Channel::Ch1);
pwm.enable(timer::Channel::Ch2);

// 获取PWM通道 - 这些实现了SetDutyCycle trait
let pwm_a = pwm.ch1();
let pwm_b = pwm.ch2();

// 创建驱动实例
let mut motor_driver = Drv8833::new(ain1, ain2, bin1, bin2, pwm_a, pwm_b);

// 控制电机
motor_driver.set_motor(Motor::A, Direction::Forward, 75)?;  // 电机A前进，75%速度
motor_driver.set_motor(Motor::B, Direction::Backward, 50)?; // 电机B后退，50%速度

// 停止所有电机
motor_driver.stop_all()?;

// 刹车所有电机
motor_driver.brake_all()?;
```

### 单电机控制示例 (使用 SimplePwmChannel)

```rust
use drv8833::{Drv8833Single, Direction};
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};

// 初始化单电机GPIO引脚
let ain1 = Output::new(p.PA0, Level::Low, Speed::Low);
let ain2 = Output::new(p.PA1, Level::Low, Speed::Low);

// 初始化PWM
let ch1 = PwmPin::new_ch1(p.PA6, embassy_stm32::gpio::OutputType::PushPull);
let mut pwm = SimplePwm::new(p.TIM3, Some(ch1), None, None, None, Hertz(10_000), Default::default());
pwm.enable(timer::Channel::Ch1);

// 获取PWM通道
let pwm_ch1 = pwm.ch1();

// 创建单电机驱动实例
let mut motor = Drv8833Single::new(ain1, ain2, pwm_ch1);

// 控制电机
motor.set_motor(Direction::Forward, 100)?;  // 全速前进
motor.set_motor(Direction::Backward, 50)?;  // 50%速度后退
motor.brake()?;                             // 刹车
motor.stop()?;                              // 滑行停止
```

## API 文档

### 方向控制

- `Direction::Forward`: 电机正转
- `Direction::Backward`: 电机反转
- `Direction::Brake`: 电机刹车（两个输入都为高）
- `Direction::Coast`: 电机滑行（两个输入都为低）

### 主要方法

#### Drv8833 (双电机)

- `new()`: 创建新的驱动实例
- `set_motor(motor, direction, duty_cycle)`: 设置指定电机的方向和速度
- `stop_all()`: 停止所有电机
- `brake_all()`: 刹车所有电机
- `motor_a_pwm()` / `motor_b_pwm()`: 获取 PWM 通道引用

#### Drv8833Single (单电机)

- `new()`: 创建新的单电机驱动实例
- `set_motor(direction, duty_cycle)`: 设置电机方向和速度
- `stop()`: 停止电机
- `brake()`: 刹车电机
- `pwm()`: 获取 PWM 通道引用

### 错误处理

驱动使用`Result<(), Error<PinError>>`进行错误处理：

- `Error::Pin(e)`: GPIO 或 PWM 操作失败
- `Error::InvalidDutyCycle`: 无效的占空比值（必须 0-100）

## 示例

查看`examples/`目录中的完整示例：

- `simple_demo.rs`: 基本 API 演示（使用 mock 实现）
- `embassy_simple_pwm.rs`: Embassy SimplePwmChannel 双电机控制示例
- `embassy_single_motor.rs`: Embassy SimplePwmChannel 单电机控制示例

运行示例：

```bash
# 运行基本演示
cargo run --example simple_demo

# 运行Embassy示例（需要硬件）
cargo run --example embassy_simple_pwm --features embassy
cargo run --example embassy_single_motor --features embassy
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
