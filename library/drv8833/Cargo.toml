[package]
name = "drv8833"
version = "0.1.0"
edition = "2024"


[dependencies]
embassy-stm32 = { version = "0.2.0", features = [
    "defmt",
    "stm32f407ve",
    "unstable-pac",
    "memory-x",
    "time-driver-tim4",
    "exti",
    "chrono",

], git = "https://github.com/embassy-rs/embassy.git", optional = true }
embassy-executor = { git = "https://github.com/embassy-rs/embassy.git", features = [
    "arch-cortex-m",
    "executor-thread",
    "defmt",
], optional = true }
embassy-time = { git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
    "defmt-timestamp-uptime",
    "tick-hz-32_768",
], optional = true }


defmt = { version = "1.0.1", optional = true }
defmt-rtt = { version = "1.0.0", optional = true }
cortex-m = { version = "0.7", features = ["inline-asm"], optional = true }
cortex-m-rt = { version = "0.7", optional = true }

[features]
default = ["embassy"]
embassy = [
    "embassy-stm32",
    "embassy-executor",
    "embassy-time",
    "defmt",
    "defmt-rtt",
    "cortex-m",
    "cortex-m-rt",
]


[[example]]
name = "stm32f103c8_mecanum"
required-features = ["embassy"]

[[example]]
name = "debug_single_motor"
required-features = ["embassy"]

[[example]]
name = "at8236_mecanum_example"
required-features = ["embassy"]

[[example]]
name = "driver_comparison"
required-features = ["embassy"]

[[example]]
name = "stm32f407vet6_mecanum"
required-features = ["embassy"]

[[example]]
name = "f407vet6_debug"
required-features = ["embassy"]

[[example]]
name = "at8236_flexible_timers"
required-features = ["embassy"]

[[example]]
name = "timer_configuration_comparison"
required-features = ["embassy"]

[lib]
test = false
bench = false
