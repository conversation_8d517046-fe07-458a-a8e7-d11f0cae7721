# DRV8833 Embassy SimplePwmChannel 使用指南

## 概述

这个 DRV8833 驱动完全兼容 Embassy 异步框架，特别是与 `SimplePwmChannel` 的集成。驱动基于 `embedded-hal` 1.0 traits，提供类型安全的电机控制接口。

## 核心特性

✅ **完全兼容 Embassy SimplePwmChannel**  
✅ **支持双电机独立控制**  
✅ **PWM 速度控制 (0-100%)**  
✅ **四种方向模式：前进、后退、刹车、滑行**  
✅ **类型安全的错误处理**  
✅ **no_std 支持**  

## 快速开始

### 1. 添加依赖

```toml
[dependencies]
drv8833 = { path = "path/to/drv8833" }
embassy-stm32 = { git = "https://github.com/embassy-rs/embassy.git", features = ["stm32f103c8"] }
embedded-hal = "1.0"
```

### 2. 基本使用模式

```rust
use drv8833::{Drv8833, Motor, Direction};
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::time::Hertz;

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());

    // GPIO 引脚初始化
    let ain1 = Output::new(p.PA0, Level::Low, Speed::Low);
    let ain2 = Output::new(p.PA1, Level::Low, Speed::Low);
    let bin1 = Output::new(p.PA2, Level::Low, Speed::Low);
    let bin2 = Output::new(p.PA3, Level::Low, Speed::Low);

    // PWM 初始化 - 关键步骤
    let ch1 = PwmPin::new(p.PA6, embassy_stm32::gpio::OutputType::PushPull);
    let ch2 = PwmPin::new(p.PA7, embassy_stm32::gpio::OutputType::PushPull);
    let mut pwm = SimplePwm::new(
        p.TIM3, 
        Some(ch1), 
        Some(ch2), 
        None, 
        None, 
        Hertz(10_000), 
        Default::default()
    );
    
    // 获取 PWM 通道 - 这些自动实现了 SetDutyCycle trait
    let pwm_a = pwm.ch1();
    let pwm_b = pwm.ch2();

    // 创建驱动实例
    let mut motor_driver = Drv8833::new(ain1, ain2, bin1, bin2, pwm_a, pwm_b);

    // 控制电机
    loop {
        motor_driver.set_motor(Motor::A, Direction::Forward, 50).unwrap();
        motor_driver.set_motor(Motor::B, Direction::Backward, 75).unwrap();
        Timer::after(Duration::from_secs(2)).await;
        
        motor_driver.stop_all().unwrap();
        Timer::after(Duration::from_secs(1)).await;
    }
}
```

## API 参考

### 方向控制
- `Direction::Forward` - 正转
- `Direction::Backward` - 反转
- `Direction::Brake` - 刹车（两个输入高电平）
- `Direction::Coast` - 滑行（两个输入低电平）

### 主要方法
- `set_motor(motor, direction, speed)` - 设置电机方向和速度
- `stop_all()` - 停止所有电机（滑行模式）
- `brake_all()` - 刹车所有电机
- `motor_a_pwm()` / `motor_b_pwm()` - 获取 PWM 通道引用

### 单电机控制
```rust
use drv8833::{Drv8833Single, Direction};

let mut motor = Drv8833Single::new(ain1, ain2, pwm_ch1);
motor.set_motor(Direction::Forward, 100).unwrap();
```

## 硬件连接

```
STM32F103    DRV8833
---------    -------
PA0      ->  AIN1     (电机A方向控制1)
PA1      ->  AIN2     (电机A方向控制2)
PA2      ->  BIN1     (电机B方向控制1)
PA3      ->  BIN2     (电机B方向控制2)
PA6      ->  PWM A    (电机A速度控制 - TIM3_CH1)
PA7      ->  PWM B    (电机B速度控制 - TIM3_CH2)
3.3V     ->  VCC      (逻辑电源)
GND      ->  GND      (接地)
```

## 示例代码

运行以下命令查看示例：

```bash
# 基本 API 演示
cargo run --example simple_demo

# Embassy 使用指南
cargo run --example embassy_usage_guide
```

## 关键优势

1. **类型安全**: 编译时检查所有错误类型
2. **零成本抽象**: 运行时无额外开销
3. **灵活配置**: 支持任意 GPIO 和 PWM 组合
4. **Embassy 原生**: 完美集成 SimplePwmChannel
5. **易于使用**: 简洁的 API 设计

## 故障排除

### 常见问题
1. **PWM 频率**: 建议使用 10kHz，避免电机噪音
2. **GPIO 速度**: 方向控制引脚使用 `Speed::Low` 即可
3. **电源**: 确保 VCC 和 VM 正确连接
4. **接地**: 所有 GND 必须连接

### 编译错误
- 确保使用 `embedded-hal = "1.0"`
- 检查 Embassy 版本兼容性
- 验证特性标志正确设置

这个驱动已经过测试，可以直接在你的 Embassy 项目中使用！
