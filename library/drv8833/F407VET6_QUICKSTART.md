# STM32F407VET6 快速入门指南

## 🚀 从 F103C8 迁移到 F407VET6

### 1. 硬件准备

#### 需要的硬件
- **STM32F407VET6 开发板** (黑色板子，100 引脚)
- **DRV8833 或 AT8236 电机驱动模块** (4 个)
- **麦克纳姆轮电机** (4 个)
- **杜邦线** 若干
- **面包板** (可选)
- **万用表** (调试用)

#### F407VET6 开发板特点
- 168MHz ARM Cortex-M4F 处理器
- 512KB Flash, 128KB RAM
- 板载 8MHz 晶振
- 板载 LED (PC13)
- USB 接口
- 更多 GPIO 引脚

### 2. 软件环境

#### 依赖已配置
库已经配置好 F407VET6 支持，无需修改 Cargo.toml：

```toml
embassy-stm32 = { features = [
    "stm32f407ve",  # ✅ 已配置
    "unstable-pac",
    "memory-x",
    "time-driver-tim4",
    # ...
]}
```

### 3. 引脚连接

#### 推荐连接方案 (TIM1 + TIM3)

```text
STM32F407VET6    DRV8833/AT8236    麦克纳姆轮
-------------------------------------------------
FL Motor (左前轮):
PE9  (TIM1_CH1) -> FL_IN1        -> FL Motor+
PE11 (TIM1_CH2) -> FL_IN2        -> FL Motor-

FR Motor (右前轮):
PE13 (TIM1_CH3) -> FR_IN1        -> FR Motor+
PE14 (TIM1_CH4) -> FR_IN2        -> FR Motor-

BL Motor (左后轮):
PC6  (TIM3_CH1) -> BL_IN1        -> BL Motor+
PC7  (TIM3_CH2) -> BL_IN2        -> BL Motor-

BR Motor (右后轮):
PC8  (TIM3_CH3) -> BR_IN1        -> BR Motor+
PC9  (TIM3_CH4) -> BR_IN2        -> BR Motor-

电源:
3.3V -> 所有 DRV8833 VCC
GND  -> 所有 DRV8833 GND
5V   -> 电机电源 (如果需要)

指示:
PC13 -> 板载 LED (无需连线)
```

### 4. 代码示例

#### 基本使用 (DRV8833)

```rust
#![no_std]
#![no_main]

use defmt::*;
use defmt_rtt as _;
use embassy_executor::Spawner;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::{bind_interrupts, peripherals, timer, Config};
use embassy_time::{Duration, Timer};
use panic_probe as _;

use drv8833::Drv8833Single;
use drv8833::mecanum::{GenericMecanumDrive, MecanumDirection};

bind_interrupts!(struct Irqs {
    TIM1_UP_TIM10 => timer::InterruptHandler<peripherals::TIM1>;
    TIM3 => timer::InterruptHandler<peripherals::TIM3>;
});

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    // F407VET6 高性能配置
    let mut config = Config::default();
    config.rcc.hse = Some(embassy_stm32::time::Hertz(8_000_000));
    // ... 其他配置见完整示例
    
    let p = embassy_stm32::init(config);
    info!("🚀 F407VET6 启动");

    // PWM 配置
    let fl_in1 = PwmPin::new_ch1(p.PE9, embassy_stm32::gpio::Pull::None);
    let fl_in2 = PwmPin::new_ch2(p.PE11, embassy_stm32::gpio::Pull::None);
    // ... 其他引脚配置

    // 创建 PWM (20kHz 高频)
    let mut pwm_tim1 = SimplePwm::new(
        p.TIM1, Some(fl_in1), Some(fl_in2), 
        Some(fr_in1), Some(fr_in2),
        embassy_stm32::time::Hertz(20_000),
        Default::default(),
    );
    pwm_tim1.enable_all();

    // 创建电机驱动器
    let fl_motor = Drv8833Single::new(pwm_tim1.ch1(), pwm_tim1.ch2());
    // ... 其他电机

    // 创建麦克纳姆驱动
    let mut mecanum = GenericMecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);

    // 控制运动
    loop {
        let _ = mecanum.move_direction(MecanumDirection::Forward, 50);
        Timer::after(Duration::from_secs(2)).await;
        
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;
    }
}
```

#### 使用 AT8236 (只需改变驱动器类型)

```rust
// 只需要改变这一行
use drv8833::at8236::At8236Single;

// 创建电机驱动器
let fl_motor = At8236Single::new(pwm_tim1.ch1(), pwm_tim1.ch2());
// 其他代码完全相同！
```

### 5. 运行示例

#### 完整麦克纳姆轮示例
```bash
cargo run --example stm32f407vet6_mecanum --features embassy
```

#### 单电机调试
```bash
cargo run --example f407vet6_debug --features embassy
```

#### 驱动器对比测试
```bash
cargo run --example driver_comparison --features embassy
```

### 6. 调试步骤

#### 第一步：验证基本功能
1. 上电后 LED (PC13) 应该亮起
2. 串口输出应该显示启动信息
3. 检查系统时钟是否为 168MHz

#### 第二步：测试单个电机
1. 运行 `f407vet6_debug` 示例
2. 观察 LED 闪烁模式
3. 用万用表测量 PWM 输出

#### 第三步：测试完整系统
1. 运行 `stm32f407vet6_mecanum` 示例
2. 观察所有电机是否同步运动
3. 测试各种运动模式

### 7. 常见问题

#### 问题 1: 编译错误
```
error: target may not support the standard library
```
**解决**: 确保使用 `#![no_std]` 和正确的 target

#### 问题 2: 电机不转
**检查清单**:
- [ ] PWM 是否启用 (`pwm.enable_all()`)
- [ ] 引脚连接是否正确
- [ ] 电源是否充足
- [ ] 驱动器是否正常

#### 问题 3: 运动不准确
**可能原因**:
- 电机方向接反
- PWM 频率不合适
- 速度设置过高/过低

### 8. 性能优化

#### F407VET6 特有优势
- **高频 PWM**: 可以使用 20kHz-50kHz
- **多定时器**: 分散负载，减少干扰
- **高精度**: 168MHz 主频提供更精确控制
- **大内存**: 可以实现更复杂的算法

#### 推荐配置
```rust
// 高性能配置
Hertz(20_000)  // PWM 频率: 20kHz (推荐)
Hertz(50_000)  // PWM 频率: 50kHz (超高性能)

// 系统时钟: 168MHz (最大性能)
// APB1: 42MHz, APB2: 84MHz
```

### 9. 扩展功能

#### 角度控制 (MPU6050 集成)
```rust
// 模拟 MPU 反馈的角度纠正
let yaw_error = mpu.get_yaw_error().await?;
let corrected = mecanum.correct_yaw_deviation(yaw_error, 30);
if corrected {
    info!("角度纠正完成");
}
```

#### 编码器反馈
```rust
// F407VET6 支持硬件编码器接口
// 可以使用 TIM 的编码器模式获取精确位置反馈
```

### 10. 下一步

1. **学习高级功能**: 编码器、CAN 总线、以太网
2. **优化算法**: PID 控制、路径规划
3. **添加传感器**: MPU6050、超声波、摄像头
4. **网络功能**: WiFi、蓝牙通信
5. **图形界面**: 通过 USB 或网络控制

### 📞 获取帮助

如果遇到问题：
1. 查看 `F407VET6_PINOUT.md` 详细引脚说明
2. 运行调试示例排查硬件问题
3. 检查 `TROUBLESHOOTING.md` 故障排除指南
4. 对比 F103C8 示例找出差异

**记住**: F407VET6 是 F103C8 的强化版，大部分代码可以直接迁移，主要是引脚和时钟配置的差异！
