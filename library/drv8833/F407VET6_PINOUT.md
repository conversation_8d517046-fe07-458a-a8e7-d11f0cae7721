# STM32F407VET6 引脚配置指南

## 🔌 F407VET6 vs F103C8 对比

| 特性 | STM32F103C8 | STM32F407VET6 | 优势 |
|------|-------------|---------------|------|
| **主频** | 72MHz | 168MHz | 2.3x 性能提升 |
| **Flash** | 64KB | 512KB | 8x 存储空间 |
| **RAM** | 20KB | 128KB | 6.4x 内存 |
| **定时器** | TIM1-4 | TIM1-14 | 更多 PWM 通道 |
| **GPIO** | 37 个 | 82 个 | 更多引脚选择 |
| **PWM 频率** | ~10kHz | 50kHz+ | 更高频率 |

## 📍 推荐引脚配置

### 方案 1: 使用 TIM1 + TIM3 (推荐)

```text
电机配置 - 高性能方案:

FL Motor (左前轮) - TIM1:
PE9  (TIM1_CH1) -> DRV8833/AT8236_FL_IN1
PE11 (TIM1_CH2) -> DRV8833/AT8236_FL_IN2

FR Motor (右前轮) - TIM1:
PE13 (TIM1_CH3) -> DRV8833/AT8236_FR_IN1
PE14 (TIM1_CH4) -> DRV8833/AT8236_FR_IN2

BL Motor (左后轮) - TIM3:
PC6  (TIM3_CH1) -> DRV8833/AT8236_BL_IN1
PC7  (TIM3_CH2) -> DRV8833/AT8236_BL_IN2

BR Motor (右后轮) - TIM3:
PC8  (TIM3_CH3) -> DRV8833/AT8236_BR_IN1
PC9  (TIM3_CH4) -> DRV8833/AT8236_BR_IN2

控制和指示:
PC13 -> LED (板载)
PA9  -> USART1_TX (调试)
PA10 -> USART1_RX (调试)
```

### 方案 2: 使用 TIM2 + TIM4 (备选)

```text
电机配置 - 备选方案:

FL Motor (左前轮) - TIM2:
PA0  (TIM2_CH1) -> DRV8833/AT8236_FL_IN1
PA1  (TIM2_CH2) -> DRV8833/AT8236_FL_IN2

FR Motor (右前轮) - TIM2:
PA2  (TIM2_CH3) -> DRV8833/AT8236_FR_IN1
PA3  (TIM2_CH4) -> DRV8833/AT8236_FR_IN2

BL Motor (左后轮) - TIM4:
PD12 (TIM4_CH1) -> DRV8833/AT8236_BL_IN1
PD13 (TIM4_CH2) -> DRV8833/AT8236_BL_IN2

BR Motor (右后轮) - TIM4:
PD14 (TIM4_CH3) -> DRV8833/AT8236_BR_IN1
PD15 (TIM4_CH4) -> DRV8833/AT8236_BR_IN2
```

### 方案 3: 分散配置 (最大灵活性)

```text
电机配置 - 分散方案:

FL Motor - TIM1: PE9 (CH1), PE11 (CH2)
FR Motor - TIM2: PA0 (CH1), PA1 (CH2)
BL Motor - TIM3: PC6 (CH1), PC7 (CH2)
BR Motor - TIM4: PD12 (CH1), PD13 (CH2)
```

## ⚡ PWM 性能配置

### 推荐 PWM 频率

| 应用场景 | 频率 | 优势 | 缺点 |
|----------|------|------|------|
| **调试测试** | 1kHz | 易于观察 | 可能有噪音 |
| **一般应用** | 10kHz | 平衡性能 | 标准选择 |
| **高性能** | 20kHz | 静音运行 | 推荐 |
| **超高性能** | 50kHz | 最佳响应 | 功耗稍高 |

### 代码配置示例

```rust
// 高性能配置 (20kHz)
let mut pwm = SimplePwm::new(
    p.TIM1,
    Some(pe9_pin),   // CH1
    Some(pe11_pin),  // CH2
    Some(pe13_pin),  // CH3
    Some(pe14_pin),  // CH4
    Hertz(20_000),   // 20kHz PWM
    Default::default(),
);

// 超高性能配置 (50kHz)
let mut pwm = SimplePwm::new(
    p.TIM1,
    Some(pe9_pin),
    Some(pe11_pin),
    Some(pe13_pin),
    Some(pe14_pin),
    Hertz(50_000),   // 50kHz PWM
    Default::default(),
);
```

## 🔧 时钟配置

### 高性能时钟设置

```rust
let mut config = Config::default();
config.rcc.hse = Some(Hertz(8_000_000)); // 外部 8MHz 晶振
config.rcc.pll_src = embassy_stm32::rcc::PllSource::HSE;
config.rcc.pll = Some(embassy_stm32::rcc::Pll {
    prediv: embassy_stm32::rcc::PllPreDiv::DIV4,
    mul: embassy_stm32::rcc::PllMul::MUL168,
    divp: Some(embassy_stm32::rcc::PllPDiv::DIV2), // 168MHz 系统时钟
    divq: Some(embassy_stm32::rcc::PllQDiv::DIV7),
    divr: None,
});
config.rcc.ahb_pre = embassy_stm32::rcc::AHBPrescaler::DIV1;
config.rcc.apb1_pre = embassy_stm32::rcc::APBPrescaler::DIV4; // 42MHz
config.rcc.apb2_pre = embassy_stm32::rcc::APBPrescaler::DIV2; // 84MHz
```

## 🔌 硬件连接

### 电源连接

```text
F407VET6 开发板:
3.3V -> DRV8833/AT8236 VCC
GND  -> DRV8833/AT8236 GND
5V   -> 电机电源 (如果需要)

注意: F407VET6 可以提供更大的电流，但仍建议使用外部电源供电大功率电机
```

### 调试连接

```text
调试接口:
PA9  (USART1_TX) -> USB-TTL 转换器 RX
PA10 (USART1_RX) -> USB-TTL 转换器 TX
GND              -> USB-TTL 转换器 GND

SWD 调试:
PA13 (SWDIO) -> ST-Link SWDIO
PA14 (SWCLK) -> ST-Link SWCLK
GND          -> ST-Link GND
3.3V         -> ST-Link 3.3V
```

## 📊 性能优化建议

### 1. 使用高级定时器 (TIM1, TIM8)
- 更多功能和更高精度
- 支持互补输出
- 死区时间控制

### 2. 合理分配定时器
- 避免所有电机使用同一个定时器
- 分散负载到不同的 APB 总线

### 3. 优化时钟配置
- 使用外部晶振 (HSE)
- 配置合适的分频比
- 确保 PWM 时钟足够高

### 4. 引脚选择原则
- 优先使用专用 PWM 引脚
- 避免与其他功能冲突
- 考虑 PCB 布线便利性

## 🧪 测试和验证

### 基本测试步骤

1. **时钟验证**
   ```rust
   info!("System clock: {}Hz", embassy_stm32::rcc::frequency().0);
   ```

2. **PWM 输出测试**
   ```rust
   // 用万用表测量引脚电压
   pwm.set_duty_cycle(pwm.max_duty_cycle() / 2); // 50% 占空比
   ```

3. **电机响应测试**
   ```rust
   motor.set_motor(Direction::Forward, 25)?; // 低速测试
   ```

### 故障排除

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 电机不转 | PWM 未启用 | 调用 `pwm.enable_all()` |
| 速度不准 | 时钟配置错误 | 检查 PLL 设置 |
| 噪音大 | PWM 频率太低 | 提高到 20kHz+ |
| 发热严重 | 频率过高 | 降低到 20kHz |

## 📝 示例代码

运行 F407VET6 专用示例：

```bash
# 完整麦克纳姆轮示例
cargo run --example stm32f407vet6_mecanum --features embassy

# 单电机调试
cargo run --example f407vet6_debug --features embassy
```

## 🔮 扩展功能

F407VET6 的额外能力：

- **编码器接口**: 使用 TIM 的编码器模式
- **CAN 总线**: 多机器人通信
- **以太网**: 网络控制
- **USB**: 高速数据传输
- **DMA**: 高效数据传输
- **浮点运算**: 硬件 FPU 支持
