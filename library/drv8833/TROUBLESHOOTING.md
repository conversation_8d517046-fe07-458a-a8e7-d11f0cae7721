# DRV8833 电机无反应故障排除指南

## 🔍 常见问题及解决方案

### 1. 电机完全没有反应

#### 可能原因 A: PWM 未启用
```rust
// ❌ 错误 - 忘记启用 PWM
let mut pwm = SimplePwm::new(p.TIM2, ...);
// 缺少这一行！

// ✅ 正确 - 必须启用 PWM
let mut pwm = SimplePwm::new(p.TIM2, ...);
pwm.enable_all(); // 🔥 关键步骤
```

#### 可能原因 B: 电源问题
- **检查 DRV8833 VCC**: 必须有 3.3V 或 5V
- **检查 GND 连接**: STM32 和 DRV8833 必须共地
- **电机电源**: 大电机需要外部电源，不能只用 STM32 供电

#### 可能原因 C: 引脚配置错误
```rust
// ✅ 确保使用正确的引脚和定时器组合
// STM32F103C8 可用的 PWM 引脚:
PA0  -> TIM2_CH1
PA1  -> TIM2_CH2
PA2  -> TIM2_CH3
PA3  -> TIM2_CH4
PA6  -> TIM3_CH1
PA7  -> TIM3_CH2
PB0  -> TIM3_CH3
PB1  -> TIM3_CH4
```

### 2. 电机只在一个方向转动

#### 可能原因: 硬件连接问题
- 检查 IN1 和 IN2 是否都正确连接
- 检查电机线是否接触良好
- 尝试交换 IN1 和 IN2 的连接

### 3. 电机转速不正确

#### 可能原因 A: PWM 频率不合适
```rust
// 尝试不同的 PWM 频率
Hertz(100)    // 低频 - 适合调试
Hertz(1_000)  // 中频 - 适合大多数电机
Hertz(10_000) // 高频 - 适合小电机
```

#### 可能原因 B: 占空比计算错误
库已经处理了占空比计算，但如果有问题，检查：
```rust
// 库内部的计算逻辑
let max_duty = pwm.max_duty_cycle();
let duty = (max_duty as u32 * speed as u32) / 100;
pwm.set_duty_cycle(duty as u16);
```

### 4. 电机抖动或不稳定

#### 可能原因 A: 电源不稳定
- 使用电容滤波
- 确保电源电流足够
- 使用独立的电机电源

#### 可能原因 B: PWM 频率太低
```rust
// 提高 PWM 频率
Hertz(10_000) // 或更高
```

## 🧪 调试步骤

### 步骤 1: 使用调试示例
```bash
cargo run --example debug_single_motor --features embassy
```

### 步骤 2: 万用表测试
1. 测量 STM32 引脚输出电压
2. 测量 DRV8833 输入引脚电压
3. 测量 DRV8833 输出引脚电压
4. 测量电机两端电压

### 步骤 3: 示波器测试 (如果有)
1. 观察 PWM 波形
2. 检查频率和占空比
3. 确认信号完整性

### 步骤 4: 替换测试
1. 尝试不同的引脚
2. 尝试不同的电机
3. 尝试不同的 DRV8833 芯片

## 🔧 硬件检查清单

### STM32F103C8 引脚检查
- [ ] PA0 连接到 DRV8833 IN1
- [ ] PA1 连接到 DRV8833 IN2
- [ ] 3.3V 连接到 DRV8833 VCC
- [ ] GND 连接到 DRV8833 GND

### DRV8833 检查
- [ ] VCC 有电压 (3.3V 或 5V)
- [ ] GND 正确连接
- [ ] IN1, IN2 有 PWM 信号
- [ ] OUT1, OUT2 连接到电机

### 电机检查
- [ ] 电机线连接牢固
- [ ] 电机本身工作正常 (直接接电源测试)
- [ ] 电机电流不超过 DRV8833 限制 (1.2A)

## 📋 测试代码模板

### 最简单的测试
```rust
// 只测试一个方向，一个速度
let _ = motor.set_motor(Direction::Forward, 50);
Timer::after(Duration::from_secs(3)).await;
let _ = motor.coast();
```

### 逐步测试
```rust
// 从低速开始测试
for speed in [10, 20, 30, 50, 80] {
    info!("Testing speed: {}%", speed);
    let _ = motor.set_motor(Direction::Forward, speed);
    Timer::after(Duration::from_secs(2)).await;
    let _ = motor.coast();
    Timer::after(Duration::from_secs(1)).await;
}
```

## 🆘 如果仍然无法解决

1. **检查库版本**: 确保使用最新版本
2. **查看示例**: 运行提供的示例代码
3. **硬件替换**: 尝试不同的硬件组合
4. **社区求助**: 在相关论坛发布详细的硬件配置和代码

## 📞 常用调试命令

```bash
# 编译检查
cargo check --lib

# 运行调试示例
cargo run --example debug_single_motor --features embassy

# 运行完整示例
cargo run --example stm32f103c8_mecanum --features embassy

# 检查代码质量
cargo clippy --lib
```

记住：**硬件问题比软件问题更常见**！先确保硬件连接正确，再检查软件配置。
