# AT8236 灵活定时器配置 - 新功能详解

## 🎉 新功能亮点

AT8236Single 现在支持**跨定时器 PWM 通道配置**！这意味着每个电机的两个 PWM 通道可以来自不同的定时器，提供前所未有的配置灵活性。

## 🔄 API 变化

### 之前 (限制性)
```rust
// 只能使用相同定时器的通道
pub struct At8236Single<TIM: GeneralInstance4Channel> {
    in1: SimplePwmChannel<'static, TIM>,
    in2: SimplePwmChannel<'static, TIM>,
}

// 创建方式
let motor = At8236Single::new(tim1_ch1, tim1_ch2); // 必须是同一定时器
```

### 现在 (灵活性)
```rust
// 可以使用不同定时器的通道
pub struct At8236Single<TIM1: GeneralInstance4Channel, TIM2: GeneralInstance4Channel> {
    in1: SimplePwmChannel<'static, TIM1>,
    in2: SimplePwmChannel<'static, TIM2>,
}

// 创建方式
let motor1 = At8236Single::new(tim1_ch1, tim1_ch2); // 相同定时器 (向后兼容)
let motor2 = At8236Single::new(tim1_ch1, tim3_ch2); // 不同定时器 (新功能!)
let motor3 = At8236Single::new(tim2_ch1, tim4_ch3); // 完全灵活配置
```

## 🎯 使用场景

### 场景 1: 引脚资源紧张
```rust
// 问题: TIM1 只有 2 个可用通道，但需要 4 个电机
// 解决: 使用跨定时器配置

// FL Motor: TIM1_CH1 + TIM2_CH1
let fl_motor = At8236Single::new(tim1_ch1, tim2_ch1);

// FR Motor: TIM1_CH2 + TIM2_CH2  
let fr_motor = At8236Single::new(tim1_ch2, tim2_ch2);

// BL Motor: TIM3_CH1 + TIM4_CH1
let bl_motor = At8236Single::new(tim3_ch1, tim4_ch1);

// BR Motor: TIM3_CH2 + TIM4_CH2
let br_motor = At8236Single::new(tim3_ch2, tim4_ch2);
```

### 场景 2: 避免定时器冲突
```rust
// 问题: 某些定时器被其他功能占用
// 解决: 灵活选择可用的定时器通道

// 假设 TIM1 被编码器占用，TIM2 被 PWM 输出占用
// 可以使用剩余的通道组合
let motor = At8236Single::new(tim3_ch1, tim4_ch2);
```

### 场景 3: 性能优化
```rust
// 分散负载到不同定时器，避免单个定时器过载
let motor1 = At8236Single::new(tim1_ch1, tim2_ch1); // 分散到 TIM1 和 TIM2
let motor2 = At8236Single::new(tim3_ch1, tim4_ch1); // 分散到 TIM3 和 TIM4
```

## 📊 配置对比

| 特性 | 传统配置 | 灵活配置 |
|------|----------|----------|
| **定时器使用** | 1-2个 | 2-4个 |
| **引脚选择** | 受限 | 最大化 |
| **资源利用** | 集中 | 分散 |
| **扩展性** | 有限 | 优秀 |
| **故障隔离** | 差 | 好 |
| **配置复杂度** | 简单 | 稍复杂 |

## 🔧 实际示例

### STM32F407VET6 灵活配置
```rust
// 引脚分配
// FL Motor: PE9 (TIM1_CH1) + PA0 (TIM2_CH1)
// FR Motor: PE11 (TIM1_CH2) + PA1 (TIM2_CH2)
// BL Motor: PC6 (TIM3_CH1) + PD12 (TIM4_CH1)
// BR Motor: PC7 (TIM3_CH2) + PD13 (TIM4_CH2)

// PWM 配置
let mut pwm_tim1 = SimplePwm::new(p.TIM1, Some(pe9), Some(pe11), None, None, Hertz(20_000), Default::default());
let mut pwm_tim2 = SimplePwm::new(p.TIM2, Some(pa0), Some(pa1), None, None, Hertz(20_000), Default::default());
let mut pwm_tim3 = SimplePwm::new(p.TIM3, Some(pc6), Some(pc7), None, None, Hertz(20_000), Default::default());
let mut pwm_tim4 = SimplePwm::new(p.TIM4, Some(pd12), Some(pd13), None, None, Hertz(20_000), Default::default());

// 启用所有 PWM
pwm_tim1.enable_all();
pwm_tim2.enable_all();
pwm_tim3.enable_all();
pwm_tim4.enable_all();

// 创建跨定时器电机驱动器
let fl_motor = At8236Single::new(pwm_tim1.ch1(), pwm_tim2.ch1()); // TIM1 + TIM2
let fr_motor = At8236Single::new(pwm_tim1.ch2(), pwm_tim2.ch2()); // TIM1 + TIM2
let bl_motor = At8236Single::new(pwm_tim3.ch1(), pwm_tim4.ch1()); // TIM3 + TIM4
let br_motor = At8236Single::new(pwm_tim3.ch2(), pwm_tim4.ch2()); // TIM3 + TIM4

// 创建麦克纳姆驱动系统 (API 完全相同!)
let mut mecanum = GenericMecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);
```

## 🧪 测试示例

### 运行灵活定时器示例
```bash
cargo run --example at8236_flexible_timers --features embassy
```

### 运行配置对比示例
```bash
cargo run --example timer_configuration_comparison --features embassy
```

## 🔄 向后兼容性

**完全向后兼容！** 现有代码无需修改：

```rust
// 旧代码仍然工作
let motor = At8236Single::new(tim1_ch1, tim1_ch2);

// 新功能是可选的
let motor = At8236Single::new(tim1_ch1, tim3_ch2); // 新功能
```

## 🎯 最佳实践

### 1. 引脚规划
```rust
// 优先使用相邻的引脚便于布线
let motor = At8236Single::new(tim1_ch1, tim1_ch2); // 相邻引脚

// 如果相邻引脚不可用，使用跨定时器
let motor = At8236Single::new(tim1_ch1, tim3_ch1); // 跨定时器
```

### 2. 性能优化
```rust
// 分散负载到不同的 APB 总线
let motor1 = At8236Single::new(tim1_ch1, tim3_ch1); // APB2 + APB1
let motor2 = At8236Single::new(tim2_ch1, tim4_ch1); // APB1 + APB1
```

### 3. 故障隔离
```rust
// 关键电机使用独立定时器
let critical_motor = At8236Single::new(tim1_ch1, tim2_ch1); // 独立定时器
let normal_motor = At8236Single::new(tim3_ch1, tim3_ch2);   // 共享定时器
```

## 🚀 高级用法

### 动态频率配置
```rust
// 不同定时器可以使用不同频率
let mut pwm_tim1 = SimplePwm::new(p.TIM1, ..., Hertz(20_000), ...); // 20kHz
let mut pwm_tim2 = SimplePwm::new(p.TIM2, ..., Hertz(10_000), ...); // 10kHz

// 电机的两个通道使用不同频率 (高级用法)
let motor = At8236Single::new(pwm_tim1.ch1(), pwm_tim2.ch1());
```

### 混合驱动器配置
```rust
// 在同一系统中混合使用 DRV8833 和 AT8236
let drv_motor = Drv8833Single::new(tim1_ch1, tim1_ch2);
let at8236_motor = At8236Single::new(tim2_ch1, tim3_ch1); // 跨定时器

let mecanum = GenericMecanumDrive::new(drv_motor, at8236_motor, ...);
```

## 🔍 调试技巧

### 1. 验证 PWM 输出
```rust
// 分别测试每个通道
motor.in1_pwm().set_duty_cycle(motor.in1_pwm().max_duty_cycle() / 2);
motor.in2_pwm().set_duty_cycle(0);
```

### 2. 定时器状态检查
```rust
// 确保所有相关定时器都已启用
pwm_tim1.enable_all();
pwm_tim2.enable_all();
// ...
```

## 📈 性能影响

### 优势
- ✅ **更好的资源利用**: 分散负载
- ✅ **降低干扰**: 独立的 PWM 信号
- ✅ **提高可靠性**: 故障隔离
- ✅ **增强扩展性**: 更多配置选项

### 考虑因素
- ⚠️ **稍微增加复杂性**: 需要管理更多定时器
- ⚠️ **内存使用**: 每个定时器有独立的状态

## 🎉 总结

AT8236 的灵活定时器配置功能为复杂的电机控制系统提供了前所未有的灵活性。无论是简单的单电机应用还是复杂的多电机系统，这个功能都能帮你找到最优的配置方案。

**关键优势:**
- 🎯 **最大化引脚选择灵活性**
- ⚡ **更好的性能和资源利用**
- 🔧 **完全向后兼容**
- 🚀 **为未来扩展做好准备**

开始使用这个强大的新功能，让你的电机控制系统更加灵活和高效！
