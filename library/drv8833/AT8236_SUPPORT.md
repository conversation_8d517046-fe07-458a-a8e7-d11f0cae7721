# AT8236 驱动支持

## 🎉 新增功能

DRV8833 库现在支持 **AT8236** 电机驱动芯片！通过统一的 `MotorDriver` trait，你可以无缝地在 DRV8833 和 AT8236 之间切换。

## 🔧 支持的驱动器

| 驱动器 | 状态 | 特性 |
|--------|------|------|
| **DRV8833** | ✅ 完全支持 | 德州仪器，1.2A 连续电流 |
| **AT8236** | ✅ 新增支持 | 双 H 桥，兼容 DRV8833 API |

## 🚀 快速开始

### 使用 AT8236

```rust
use drv8833::at8236::At8236Single;
use drv8833::mecanum::GenericMecanumDrive;

// 创建 AT8236 电机驱动器
let fl_motor = At8236Single::new(fl_pwm_in1, fl_pwm_in2);
let fr_motor = At8236Single::new(fr_pwm_in1, fr_pwm_in2);
let bl_motor = At8236Single::new(bl_pwm_in1, bl_pwm_in2);
let br_motor = At8236Single::new(br_pwm_in1, br_pwm_in2);

// 创建麦克纳姆驱动系统
let mut mecanum = GenericMecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);

// 使用相同的 API！
mecanum.move_direction(MecanumDirection::Forward, 50)?;
mecanum.correct_yaw_deviation(yaw_error, 30);
```

### 使用 DRV8833 (原有方式)

```rust
use drv8833::Drv8833Single;
use drv8833::mecanum::GenericMecanumDrive;

// 创建 DRV8833 电机驱动器
let fl_motor = Drv8833Single::new(fl_pwm_in1, fl_pwm_in2);
let fr_motor = Drv8833Single::new(fr_pwm_in1, fr_pwm_in2);
let bl_motor = Drv8833Single::new(bl_pwm_in1, bl_pwm_in2);
let br_motor = Drv8833Single::new(br_pwm_in1, br_pwm_in2);

// 创建麦克纳姆驱动系统
let mut mecanum = GenericMecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);

// 完全相同的 API！
mecanum.move_direction(MecanumDirection::Forward, 50)?;
mecanum.correct_yaw_deviation(yaw_error, 30);
```

## 🔄 驱动器切换

### 方法 1: 编译时切换

```rust
// 在代码中选择驱动器类型
const USE_AT8236: bool = true; // true = AT8236, false = DRV8833

if USE_AT8236 {
    let motor = At8236Single::new(pwm1, pwm2);
} else {
    let motor = Drv8833Single::new(pwm1, pwm2);
}
```

### 方法 2: 类型别名

```rust
// 定义类型别名便于切换
#[cfg(feature = "at8236")]
type MotorType<TIM> = At8236Single<TIM>;

#[cfg(not(feature = "at8236"))]
type MotorType<TIM> = Drv8833Single<TIM>;

// 使用统一的类型
let motor = MotorType::new(pwm1, pwm2);
```

## 📊 API 兼容性

| 功能 | DRV8833 | AT8236 | 说明 |
|------|---------|--------|------|
| `set_motor()` | ✅ | ✅ | 设置方向和速度 |
| `brake()` | ✅ | ✅ | 刹车 |
| `coast()` | ✅ | ✅ | 滑行停止 |
| 麦克纳姆轮控制 | ✅ | ✅ | 完全兼容 |
| 角度纠正 | ✅ | ✅ | MPU 集成 |

## 🧪 示例代码

### 1. AT8236 基本使用
```bash
cargo run --example at8236_mecanum_example --features embassy
```

### 2. 驱动器对比
```bash
cargo run --example driver_comparison --features embassy
```

### 3. STM32F103C8 完整示例
```bash
cargo run --example stm32f103c8_mecanum --features embassy
```

## 🔌 硬件连接

AT8236 和 DRV8833 使用相同的连接方式：

```text
STM32F103C8    AT8236/DRV8833    Motor
PA0        ->  IN1           ->   M+
PA1        ->  IN2           ->   M-
3.3V       ->  VCC
GND        ->  GND
```

## ⚡ 性能对比

| 特性 | DRV8833 | AT8236 | 备注 |
|------|---------|--------|------|
| 连续电流 | 1.2A | 待确认 | 查看数据手册 |
| 峰值电流 | 2.8A | 待确认 | 查看数据手册 |
| 工作电压 | 2.7-10.8V | 待确认 | 查看数据手册 |
| PWM 频率 | 最高 250kHz | 待确认 | 查看数据手册 |

## 🛠️ 技术实现

### MotorDriver Trait

```rust
pub trait MotorDriver {
    type Error;
    
    fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), Self::Error>;
    fn brake(&mut self) -> Result<(), Self::Error>;
    fn coast(&mut self) -> Result<(), Self::Error>;
}
```

### 自动实现

- `Drv8833Single<TIM>` 自动实现 `MotorDriver`
- `At8236Single<TIM>` 自动实现 `MotorDriver`
- `GenericMecanumDrive<FL, FR, BL, BR>` 接受任何 `MotorDriver`

## 🔮 未来计划

- [ ] 添加更多电机驱动器支持 (L298N, TB6612FNG 等)
- [ ] 性能基准测试
- [ ] 自动驱动器检测
- [ ] 配置文件支持

## 📝 迁移指南

### 从旧版本迁移

如果你使用的是旧版本的 `MecanumDrive`，可以轻松迁移到新的 `GenericMecanumDrive`：

```rust
// 旧版本
let mecanum = MecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);

// 新版本 (向后兼容)
let mecanum = GenericMecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);
```

### 添加 AT8236 支持

```rust
// 只需要改变导入和创建
use drv8833::at8236::At8236Single;  // 新增这行
// use drv8833::Drv8833Single;      // 注释掉旧的

let motor = At8236Single::new(pwm1, pwm2);  // 改变这里
// let motor = Drv8833Single::new(pwm1, pwm2);  // 旧版本
```

其他代码保持不变！

## 🤝 贡献

欢迎为库添加更多电机驱动器支持！请参考 `at8236.rs` 的实现方式。
