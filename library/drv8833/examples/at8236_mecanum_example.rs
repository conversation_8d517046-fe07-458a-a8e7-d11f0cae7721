//! AT8236 麦克纳姆轮驱动示例 - F407VET6
//!
//! 展示如何使用 AT8236 驱动芯片控制麦克纳姆轮

#![no_std]
#![no_main]

use defmt::*;
use defmt_rtt as _;
use embassy_executor::Spawner;
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::{Config, bind_interrupts, peripherals, timer};
use embassy_time::{Duration, Timer};
use panic_probe as _;

use drv8833::at8236::At8236Single;
use drv8833::mecanum::{GenericMecanumDrive, MecanumDirection};

bind_interrupts!(struct Irqs {
    TIM1_UP_TIM10 => timer::InterruptHandler<peripherals::TIM1>;
    TIM3 => timer::InterruptHandler<peripherals::TIM3>;
});

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());
    info!("🚀 AT8236 麦克纳姆轮驱动启动");

    // PWM 引脚配置
    let fl_in1_pin = PwmPin::new_ch1(p.PE9, embassy_stm32::gpio::Pull::None);
    let fl_in2_pin = PwmPin::new_ch2(p.PE11, embassy_stm32::gpio::Pull::None);
    let fr_in1_pin = PwmPin::new_ch3(p.PE13, embassy_stm32::gpio::Pull::None);
    let fr_in2_pin = PwmPin::new_ch4(p.PE14, embassy_stm32::gpio::Pull::None);
    let bl_in1_pin = PwmPin::new_ch1(p.PC6, embassy_stm32::gpio::Pull::None);
    let bl_in2_pin = PwmPin::new_ch2(p.PC7, embassy_stm32::gpio::Pull::None);
    let br_in1_pin = PwmPin::new_ch3(p.PC8, embassy_stm32::gpio::Pull::None);
    let br_in2_pin = PwmPin::new_ch4(p.PC9, embassy_stm32::gpio::Pull::None);

    // 创建 PWM
    let mut pwm_tim1 = SimplePwm::new(
        p.TIM1,
        Some(fl_in1_pin),
        Some(fl_in2_pin),
        Some(fr_in1_pin),
        Some(fr_in2_pin),
        Hertz(20_000),
        Default::default(),
    );

    let mut pwm_tim3 = SimplePwm::new(
        p.TIM3,
        Some(bl_in1_pin),
        Some(bl_in2_pin),
        Some(br_in1_pin),
        Some(br_in2_pin),
        Hertz(20_000),
        Default::default(),
    );

    pwm_tim1.enable_all();
    pwm_tim3.enable_all();

    // 创建 AT8236 电机驱动器 (使用相同定时器的通道)
    let fl_motor = At8236Single::new(pwm_tim1.ch1(), pwm_tim1.ch2());
    let fr_motor = At8236Single::new(pwm_tim1.ch3(), pwm_tim1.ch4());
    let bl_motor = At8236Single::new(pwm_tim3.ch1(), pwm_tim3.ch2());
    let br_motor = At8236Single::new(pwm_tim3.ch3(), pwm_tim3.ch4());

    // 💡 新功能提示: 也可以使用跨定时器配置!
    // let fl_motor = At8236Single::new(pwm_tim1.ch1(), pwm_tim3.ch1());
    // let fr_motor = At8236Single::new(pwm_tim1.ch2(), pwm_tim3.ch2());
    // 查看 at8236_flexible_timers.rs 示例了解更多

    // 创建麦克纳姆驱动系统
    let mut mecanum = GenericMecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);

    info!("✅ AT8236 麦克纳姆驱动系统初始化完成");

    loop {
        info!("🔄 AT8236 前进");
        let _ = mecanum.move_direction(MecanumDirection::Forward, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("🔄 AT8236 后退");
        let _ = mecanum.move_direction(MecanumDirection::Backward, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("🔄 AT8236 左平移");
        let _ = mecanum.move_direction(MecanumDirection::Left, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("🔄 AT8236 右平移");
        let _ = mecanum.move_direction(MecanumDirection::Right, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("⏸️ AT8236 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;
    }
}
