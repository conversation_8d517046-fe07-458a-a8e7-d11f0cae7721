//! 定时器配置对比示例
//!
//! 展示 AT8236Single 的两种配置方式：
//! 1. 传统方式：相同定时器的通道
//! 2. 新方式：不同定时器的通道
//!
//! 通过编译时选择来对比两种方式的差异

#![no_std]
#![no_main]

use defmt::*;
use defmt_rtt as _;
use embassy_executor::Spawner;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::{bind_interrupts, peripherals, timer, Config};
use embassy_time::{Duration, Timer};
use panic_probe as _;

use drv8833::at8236::At8236Single;

bind_interrupts!(struct Irqs {
    TIM1_UP_TIM10 => timer::InterruptHandler<peripherals::TIM1>;
    TIM2 => timer::InterruptHandler<peripherals::TIM2>;
    TIM3 => timer::InterruptHandler<peripherals::TIM3>;
});

// 编译时选择配置方式
const USE_FLEXIBLE_TIMERS: bool = true; // true = 跨定时器, false = 传统方式

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());
    
    if USE_FLEXIBLE_TIMERS {
        info!("🚀 使用灵活定时器配置 (跨定时器)");
    } else {
        info!("🚀 使用传统定时器配置 (相同定时器)");
    }

    let mut led = Output::new(p.PC13, Level::High, Speed::Low);
    led.set_low();

    if USE_FLEXIBLE_TIMERS {
        // 🎯 新方式：跨定时器配置
        run_flexible_timer_demo(p, &mut led).await;
    } else {
        // 📦 传统方式：相同定时器配置
        run_traditional_timer_demo(p, &mut led).await;
    }
}

/// 灵活定时器配置演示 - 每个电机使用不同定时器的通道
async fn run_flexible_timer_demo(p: embassy_stm32::Peripherals, led: &mut Output<'_, embassy_stm32::gpio::Pin>) {
    info!("📌 灵活配置: 电机使用来自不同定时器的 PWM 通道");
    info!("  Motor IN1: TIM1_CH1 (PE9)");
    info!("  Motor IN2: TIM2_CH1 (PA0)");
    info!("  优势: 最大引脚选择灵活性");

    // 配置来自不同定时器的引脚
    let in1_pin = PwmPin::new_ch1(p.PE9, embassy_stm32::gpio::Pull::None);  // TIM1_CH1
    let in2_pin = PwmPin::new_ch1(p.PA0, embassy_stm32::gpio::Pull::None);  // TIM2_CH1

    // 创建两个独立的定时器
    let mut pwm_tim1 = SimplePwm::new(
        p.TIM1,
        Some(in1_pin),
        None,
        None,
        None,
        Hertz(20_000),
        Default::default(),
    );

    let mut pwm_tim2 = SimplePwm::new(
        p.TIM2,
        Some(in2_pin),
        None,
        None,
        None,
        Hertz(20_000),
        Default::default(),
    );

    pwm_tim1.enable_all();
    pwm_tim2.enable_all();

    // 🎯 创建跨定时器的电机驱动器 (新功能!)
    let mut motor = At8236Single::new(pwm_tim1.ch1(), pwm_tim2.ch1());

    info!("✅ 跨定时器电机驱动器创建成功");
    info!("📊 使用定时器: TIM1 + TIM2");

    // 测试跨定时器配置
    loop {
        info!("🔄 跨定时器测试循环");

        // 前进测试
        info!("  跨定时器前进测试");
        match motor.set_motor(drv8833::at8236::Direction::Forward, 50) {
            Ok(_) => {
                info!("    ✅ 跨定时器前进成功");
                led.set_high();
            }
            Err(e) => error!("    ❌ 跨定时器前进失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 后退测试
        info!("  跨定时器后退测试");
        match motor.set_motor(drv8833::at8236::Direction::Backward, 50) {
            Ok(_) => {
                info!("    ✅ 跨定时器后退成功");
                led.set_low();
            }
            Err(e) => error!("    ❌ 跨定时器后退失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 停止测试
        info!("  跨定时器停止测试");
        match motor.coast() {
            Ok(_) => info!("    ✅ 跨定时器停止成功"),
            Err(e) => error!("    ❌ 跨定时器停止失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(1)).await;

        info!("🎉 跨定时器配置工作正常!");
        Timer::after(Duration::from_secs(2)).await;
    }
}

/// 传统定时器配置演示 - 电机使用相同定时器的通道
async fn run_traditional_timer_demo(p: embassy_stm32::Peripherals, led: &mut Output<'_, embassy_stm32::gpio::Pin>) {
    info!("📌 传统配置: 电机使用相同定时器的 PWM 通道");
    info!("  Motor IN1: TIM1_CH1 (PE9)");
    info!("  Motor IN2: TIM1_CH2 (PE11)");
    info!("  限制: 受限于单个定时器的通道数量");

    // 配置来自相同定时器的引脚
    let in1_pin = PwmPin::new_ch1(p.PE9, embassy_stm32::gpio::Pull::None);   // TIM1_CH1
    let in2_pin = PwmPin::new_ch2(p.PE11, embassy_stm32::gpio::Pull::None);  // TIM1_CH2

    // 创建单个定时器
    let mut pwm_tim1 = SimplePwm::new(
        p.TIM1,
        Some(in1_pin),
        Some(in2_pin),
        None,
        None,
        Hertz(20_000),
        Default::default(),
    );

    pwm_tim1.enable_all();

    // 📦 创建传统的电机驱动器 (相同定时器)
    let mut motor = At8236Single::new(pwm_tim1.ch1(), pwm_tim1.ch2());

    info!("✅ 传统电机驱动器创建成功");
    info!("📊 使用定时器: 仅 TIM1");

    // 测试传统配置
    loop {
        info!("🔄 传统配置测试循环");

        // 前进测试
        info!("  传统前进测试");
        match motor.set_motor(drv8833::at8236::Direction::Forward, 50) {
            Ok(_) => {
                info!("    ✅ 传统前进成功");
                led.set_high();
            }
            Err(e) => error!("    ❌ 传统前进失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 后退测试
        info!("  传统后退测试");
        match motor.set_motor(drv8833::at8236::Direction::Backward, 50) {
            Ok(_) => {
                info!("    ✅ 传统后退成功");
                led.set_low();
            }
            Err(e) => error!("    ❌ 传统后退失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 停止测试
        info!("  传统停止测试");
        match motor.coast() {
            Ok(_) => info!("    ✅ 传统停止成功"),
            Err(e) => error!("    ❌ 传统停止失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(1)).await;

        info!("📦 传统配置工作正常 (但灵活性有限)");
        Timer::after(Duration::from_secs(2)).await;
    }
}

// 配置对比总结
/*
## 📊 配置方式对比

### 传统方式 (相同定时器)
```rust
// 限制：必须使用同一定时器的通道
let motor = At8236Single::new(tim1_ch1, tim1_ch2);
```

**优点:**
- 简单直接
- 资源集中管理
- 传统做法，文档多

**缺点:**
- 引脚选择受限
- 定时器通道数量限制
- 扩展性差
- 可能的资源冲突

### 新方式 (跨定时器)
```rust
// 灵活：可以使用任意定时器的通道
let motor = At8236Single::new(tim1_ch1, tim2_ch1);
let motor = At8236Single::new(tim1_ch1, tim3_ch2);
let motor = At8236Single::new(tim2_ch1, tim4_ch3);
```

**优点:**
- 最大引脚选择灵活性
- 更好的资源利用
- 避免定时器冲突
- 优秀的扩展性
- 故障隔离

**缺点:**
- 稍微复杂一些
- 需要管理多个定时器

## 🎯 使用建议

### 选择传统方式的情况:
- 简单的单电机应用
- 引脚资源充足
- 不需要扩展

### 选择新方式的情况:
- 多电机复杂系统
- 引脚资源紧张
- 需要最大灵活性
- 高性能要求
- 未来可能扩展

## 🔧 迁移指南

从传统方式迁移到新方式很简单：

```rust
// 旧代码
let motor = At8236Single::new(tim1_ch1, tim1_ch2);

// 新代码 - 只需要改变通道来源
let motor = At8236Single::new(tim1_ch1, tim2_ch1);
```

其他代码完全不需要改变！
*/
