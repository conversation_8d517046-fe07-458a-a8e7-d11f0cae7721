//! STM32F407VET6 单电机调试示例
//!
//! 专门为 F407VET6 设计的调试工具，利用其强大的性能进行详细的电机测试。
//!
//! # F407VET6 优势
//! - 168MHz 主频
//! - 更多定时器和 PWM 通道
//! - 更精确的时序控制
//! - 板载 LED 指示

#![no_std]
#![no_main]

use defmt::*;
use defmt_rtt as _;
use embassy_executor::Spawner;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::{bind_interrupts, peripherals, timer, Config};
use embassy_time::{Duration, Timer};
use panic_probe as _;

use drv8833::{Direction, Drv8833Single};

bind_interrupts!(struct Irqs {
    TIM1_UP_TIM10 => timer::InterruptHandler<peripherals::TIM1>;
});

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    // F407VET6 高性能配置
    let mut config = Config::default();
    config.rcc.hse = Some(Hertz(8_000_000));
    config.rcc.pll_src = embassy_stm32::rcc::PllSource::HSE;
    config.rcc.pll = Some(embassy_stm32::rcc::Pll {
        prediv: embassy_stm32::rcc::PllPreDiv::DIV4,
        mul: embassy_stm32::rcc::PllMul::MUL168,
        divp: Some(embassy_stm32::rcc::PllPDiv::DIV2), // 168MHz
        divq: Some(embassy_stm32::rcc::PllQDiv::DIV7),
        divr: None,
    });
    config.rcc.ahb_pre = embassy_stm32::rcc::AHBPrescaler::DIV1;
    config.rcc.apb1_pre = embassy_stm32::rcc::APBPrescaler::DIV4;
    config.rcc.apb2_pre = embassy_stm32::rcc::APBPrescaler::DIV2;

    let p = embassy_stm32::init(config);
    info!("🔧 F407VET6 单电机调试启动 @ 168MHz");

    // 板载 LED
    let mut led = Output::new(p.PC13, Level::High, Speed::Low);
    led.set_low();

    // 🔍 F407VET6 调试信息
    info!("📋 F407VET6 调试清单:");
    info!("  1. 系统时钟: 168MHz");
    info!("  2. PWM 频率: 可达 50kHz+");
    info!("  3. 定时器: TIM1 (高级定时器)");
    info!("  4. 引脚: PE9/PE11 (TIM1_CH1/CH2)");
    info!("  5. 板载 LED: PC13");

    // 使用 F407VET6 的高性能引脚
    // PE9 (TIM1_CH1) -> DRV8833_IN1
    // PE11 (TIM1_CH2) -> DRV8833_IN2
    let in1_pin = PwmPin::new_ch1(p.PE9, embassy_stm32::gpio::Pull::None);
    let in2_pin = PwmPin::new_ch2(p.PE11, embassy_stm32::gpio::Pull::None);

    info!("📌 F407VET6 引脚配置: PE9 (IN1), PE11 (IN2)");

    // 创建高频 PWM - 测试不同频率
    let pwm_frequencies = [
        Hertz(1_000),   // 1kHz - 基础频率
        Hertz(10_000),  // 10kHz - 中等频率
        Hertz(20_000),  // 20kHz - 高频率
        Hertz(50_000),  // 50kHz - 超高频率 (F407 可以支持)
    ];

    for (i, &freq) in pwm_frequencies.iter().enumerate() {
        info!("🧪 测试 PWM 频率: {}Hz", freq.0);
        
        let mut pwm = SimplePwm::new(
            match i {
                0 => p.TIM1,
                1 => p.TIM1, // 重复使用同一个定时器进行测试
                2 => p.TIM1,
                3 => p.TIM1,
                _ => unreachable!(),
            },
            Some(in1_pin),
            Some(in2_pin),
            None,
            None,
            freq,
            Default::default(),
        );

        pwm.enable_all();
        info!("⚡ PWM 启用完成 ({}Hz)", freq.0);

        let pwm_in1 = pwm.ch1();
        let pwm_in2 = pwm.ch2();

        let mut motor = Drv8833Single::new(pwm_in1, pwm_in2);
        info!("🎯 电机驱动器创建完成");

        // 频率特定测试
        info!("🔄 {}Hz 频率测试开始", freq.0);

        // LED 指示当前测试
        for _ in 0..(i + 1) {
            led.set_high();
            Timer::after(Duration::from_millis(100)).await;
            led.set_low();
            Timer::after(Duration::from_millis(100)).await;
        }

        // 测试不同速度
        let test_speeds = [10, 25, 50, 75, 90];
        for &speed in &test_speeds {
            info!("  测试速度: {}% @ {}Hz", speed, freq.0);
            
            // 前进测试
            match motor.set_motor(Direction::Forward, speed) {
                Ok(_) => {
                    info!("    ✅ 前进 {}% 成功", speed);
                    led.set_high();
                }
                Err(e) => {
                    error!("    ❌ 前进 {}% 失败: {:?}", speed, e);
                    led.set_low();
                }
            }
            Timer::after(Duration::from_millis(800)).await;

            // 后退测试
            match motor.set_motor(Direction::Backward, speed) {
                Ok(_) => {
                    info!("    ✅ 后退 {}% 成功", speed);
                    led.toggle();
                }
                Err(e) => {
                    error!("    ❌ 后退 {}% 失败: {:?}", speed, e);
                }
            }
            Timer::after(Duration::from_millis(800)).await;

            // 停止
            let _ = motor.coast();
            led.set_low();
            Timer::after(Duration::from_millis(200)).await;
        }

        info!("✅ {}Hz 频率测试完成", freq.0);
        Timer::after(Duration::from_secs(1)).await;
    }

    // 连续运行测试 - 使用最佳频率
    info!("🚀 F407VET6 连续运行测试 (20kHz)");
    
    let mut pwm = SimplePwm::new(
        p.TIM1,
        Some(in1_pin),
        Some(in2_pin),
        None,
        None,
        Hertz(20_000), // 使用 20kHz 作为最佳频率
        Default::default(),
    );
    pwm.enable_all();

    let pwm_in1 = pwm.ch1();
    let pwm_in2 = pwm.ch2();
    let mut motor = Drv8833Single::new(pwm_in1, pwm_in2);

    loop {
        info!("🔄 F407VET6 连续测试循环");

        // 渐变速度测试
        info!("  渐变加速测试");
        for speed in (5..=80).step_by(5) {
            let _ = motor.set_motor(Direction::Forward, speed);
            led.toggle();
            Timer::after(Duration::from_millis(200)).await;
        }

        info!("  渐变减速测试");
        for speed in (5..=80).step_by(5).rev() {
            let _ = motor.set_motor(Direction::Forward, speed);
            led.toggle();
            Timer::after(Duration::from_millis(200)).await;
        }

        // 方向切换测试
        info!("  快速方向切换测试");
        for _ in 0..5 {
            let _ = motor.set_motor(Direction::Forward, 40);
            led.set_high();
            Timer::after(Duration::from_millis(300)).await;
            
            let _ = motor.set_motor(Direction::Backward, 40);
            led.set_low();
            Timer::after(Duration::from_millis(300)).await;
        }

        // 刹车测试
        info!("  刹车测试");
        let _ = motor.set_motor(Direction::Forward, 60);
        Timer::after(Duration::from_millis(500)).await;
        let _ = motor.brake();
        led.set_high();
        Timer::after(Duration::from_millis(200)).await;
        let _ = motor.coast();
        led.set_low();

        info!("✅ F407VET6 测试循环完成");
        Timer::after(Duration::from_secs(2)).await;
    }
}

// F407VET6 调试提示
/*
## F407VET6 特定调试

### 硬件优势：
1. **高主频**: 168MHz 提供更精确的时序
2. **多定时器**: TIM1-TIM14 可用
3. **高频 PWM**: 支持 50kHz+ PWM 频率
4. **丰富引脚**: 更多 PWM 引脚选择

### 推荐配置：
- **PWM 频率**: 20kHz (平衡性能和兼容性)
- **定时器**: TIM1 (高级定时器，功能最强)
- **引脚**: PE9/PE11 (TIM1_CH1/CH2)

### 调试步骤：
1. 检查系统时钟是否正确 (168MHz)
2. 验证 PWM 输出频率和占空比
3. 测试不同频率下的电机响应
4. 观察 LED 指示状态

### 万用表测试点：
- PE9: PWM 输出 (IN1)
- PE11: PWM 输出 (IN2)
- PC13: LED 状态指示
- 3.3V: 电源电压
- GND: 地线

### 示波器测试 (如果有)：
- 观察 PWM 波形质量
- 检查频率准确性
- 验证占空比线性度
- 测量上升/下降时间
*/
