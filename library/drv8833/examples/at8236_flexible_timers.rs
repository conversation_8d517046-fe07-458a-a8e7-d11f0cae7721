//! AT8236 灵活定时器配置示例
//!
//! 展示 AT8236Single 如何使用来自不同定时器的 PWM 通道，
//! 提供最大的引脚配置灵活性。
//!
//! # 新功能亮点
//! - 每个电机的两个 PWM 通道可以来自不同定时器
//! - 更灵活的引脚选择
//! - 更好的资源利用
//! - 避免定时器冲突

#![no_std]
#![no_main]

use defmt::*;
use defmt_rtt as _;
use embassy_executor::Spawner;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::{bind_interrupts, peripherals, timer, Config};
use embassy_time::{Duration, Timer};
use panic_probe as _;

use drv8833::at8236::At8236Single;
use drv8833::mecanum::{GenericMecanumDrive, MecanumDirection};

bind_interrupts!(struct Irqs {
    TIM1_UP_TIM10 => timer::InterruptHandler<peripherals::TIM1>;
    TIM2 => timer::InterruptHandler<peripherals::TIM2>;
    TIM3 => timer::InterruptHandler<peripherals::TIM3>;
    TIM4 => timer::InterruptHandler<peripherals::TIM4>;
});

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());
    info!("🚀 AT8236 灵活定时器配置示例启动");

    // 板载 LED 指示
    let mut led = Output::new(p.PC13, Level::High, Speed::Low);
    led.set_low();

    // 🎯 灵活的引脚配置 - 每个电机使用不同定时器的组合
    info!("📌 配置灵活的 PWM 引脚分配:");
    info!("  FL Motor: TIM1_CH1 + TIM2_CH1 (跨定时器)");
    info!("  FR Motor: TIM1_CH2 + TIM2_CH2 (跨定时器)");
    info!("  BL Motor: TIM3_CH1 + TIM4_CH1 (跨定时器)");
    info!("  BR Motor: TIM3_CH2 + TIM4_CH2 (跨定时器)");

    // FL Motor - 使用 TIM1_CH1 + TIM2_CH1
    let fl_in1_pin = PwmPin::new_ch1(p.PE9, embassy_stm32::gpio::Pull::None);   // TIM1_CH1
    let fl_in2_pin = PwmPin::new_ch1(p.PA0, embassy_stm32::gpio::Pull::None);   // TIM2_CH1
    
    // FR Motor - 使用 TIM1_CH2 + TIM2_CH2
    let fr_in1_pin = PwmPin::new_ch2(p.PE11, embassy_stm32::gpio::Pull::None);  // TIM1_CH2
    let fr_in2_pin = PwmPin::new_ch2(p.PA1, embassy_stm32::gpio::Pull::None);   // TIM2_CH2
    
    // BL Motor - 使用 TIM3_CH1 + TIM4_CH1
    let bl_in1_pin = PwmPin::new_ch1(p.PC6, embassy_stm32::gpio::Pull::None);   // TIM3_CH1
    let bl_in2_pin = PwmPin::new_ch1(p.PD12, embassy_stm32::gpio::Pull::None);  // TIM4_CH1
    
    // BR Motor - 使用 TIM3_CH2 + TIM4_CH2
    let br_in1_pin = PwmPin::new_ch2(p.PC7, embassy_stm32::gpio::Pull::None);   // TIM3_CH2
    let br_in2_pin = PwmPin::new_ch2(p.PD13, embassy_stm32::gpio::Pull::None);  // TIM4_CH2

    // 创建四个独立的 PWM 定时器
    let mut pwm_tim1 = SimplePwm::new(
        p.TIM1,
        Some(fl_in1_pin),  // CH1 - FL_IN1
        Some(fr_in1_pin),  // CH2 - FR_IN1
        None,
        None,
        Hertz(20_000),
        Default::default(),
    );

    let mut pwm_tim2 = SimplePwm::new(
        p.TIM2,
        Some(fl_in2_pin),  // CH1 - FL_IN2
        Some(fr_in2_pin),  // CH2 - FR_IN2
        None,
        None,
        Hertz(20_000),
        Default::default(),
    );

    let mut pwm_tim3 = SimplePwm::new(
        p.TIM3,
        Some(bl_in1_pin),  // CH1 - BL_IN1
        Some(br_in1_pin),  // CH2 - BR_IN1
        None,
        None,
        Hertz(20_000),
        Default::default(),
    );

    let mut pwm_tim4 = SimplePwm::new(
        p.TIM4,
        Some(bl_in2_pin),  // CH1 - BL_IN2
        Some(br_in2_pin),  // CH2 - BR_IN2
        None,
        None,
        Hertz(20_000),
        Default::default(),
    );

    // 启用所有 PWM
    pwm_tim1.enable_all();
    pwm_tim2.enable_all();
    pwm_tim3.enable_all();
    pwm_tim4.enable_all();

    info!("⚡ 四个独立定时器的 PWM 启用完成");

    // 🎯 创建跨定时器的 AT8236 电机驱动器
    // 这是新功能的核心 - 每个电机的两个 PWM 通道来自不同定时器!
    
    // FL Motor: TIM1_CH1 + TIM2_CH1
    let fl_motor = At8236Single::new(pwm_tim1.ch1(), pwm_tim2.ch1());
    
    // FR Motor: TIM1_CH2 + TIM2_CH2
    let fr_motor = At8236Single::new(pwm_tim1.ch2(), pwm_tim2.ch2());
    
    // BL Motor: TIM3_CH1 + TIM4_CH1
    let bl_motor = At8236Single::new(pwm_tim3.ch1(), pwm_tim4.ch1());
    
    // BR Motor: TIM3_CH2 + TIM4_CH2
    let br_motor = At8236Single::new(pwm_tim3.ch2(), pwm_tim4.ch2());

    // 创建麦克纳姆驱动系统
    let mut mecanum = GenericMecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);

    info!("🎯 AT8236 跨定时器麦克纳姆驱动系统初始化完成");
    info!("📊 配置: 4 电机, 8 PWM 通道, 4 独立定时器");
    info!("✨ 新功能: 每个电机使用来自不同定时器的 PWM 通道");

    // LED 闪烁表示准备就绪
    for _ in 0..5 {
        led.set_high();
        Timer::after(Duration::from_millis(100)).await;
        led.set_low();
        Timer::after(Duration::from_millis(100)).await;
    }

    // 🧪 测试跨定时器配置的性能
    loop {
        info!("🔄 跨定时器配置测试开始");

        // 测试 1: 基本运动
        info!("  测试 1: 基本运动 (跨定时器)");
        
        info!("    前进");
        let _ = mecanum.move_direction(MecanumDirection::Forward, 50);
        led.set_high();
        Timer::after(Duration::from_secs(2)).await;

        info!("    后退");
        let _ = mecanum.move_direction(MecanumDirection::Backward, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("    左平移");
        let _ = mecanum.move_direction(MecanumDirection::Left, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("    右平移");
        let _ = mecanum.move_direction(MecanumDirection::Right, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("    顺时针旋转");
        let _ = mecanum.move_direction(MecanumDirection::RotateClockwise, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("    逆时针旋转");
        let _ = mecanum.move_direction(MecanumDirection::RotateCounterClockwise, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("    停止");
        let _ = mecanum.stop_all();
        led.set_low();
        Timer::after(Duration::from_secs(1)).await;

        // 测试 2: 独立电机控制 (展示跨定时器的优势)
        info!("  测试 2: 独立电机控制 (跨定时器优势)");
        
        // 每个电机独立测试，展示不同定时器的协调工作
        info!("    FL 电机 (TIM1+TIM2)");
        let _ = mecanum.fl_motor().set_motor(drv8833::at8236::Direction::Forward, 40);
        led.toggle();
        Timer::after(Duration::from_millis(800)).await;
        let _ = mecanum.fl_motor().coast();

        info!("    FR 电机 (TIM1+TIM2)");
        let _ = mecanum.fr_motor().set_motor(drv8833::at8236::Direction::Forward, 40);
        led.toggle();
        Timer::after(Duration::from_millis(800)).await;
        let _ = mecanum.fr_motor().coast();

        info!("    BL 电机 (TIM3+TIM4)");
        let _ = mecanum.bl_motor().set_motor(drv8833::at8236::Direction::Forward, 40);
        led.toggle();
        Timer::after(Duration::from_millis(800)).await;
        let _ = mecanum.bl_motor().coast();

        info!("    BR 电机 (TIM3+TIM4)");
        let _ = mecanum.br_motor().set_motor(drv8833::at8236::Direction::Forward, 40);
        led.toggle();
        Timer::after(Duration::from_millis(800)).await;
        let _ = mecanum.br_motor().coast();

        // 测试 3: 角度纠正 (跨定时器协调)
        info!("  测试 3: 角度纠正 (跨定时器协调)");
        let mut yaw_deviation = 10.0;
        let mut correction_count = 0;
        
        while !mecanum.correct_yaw_deviation(yaw_deviation, 45) && correction_count < 10 {
            Timer::after(Duration::from_millis(100)).await;
            yaw_deviation -= 1.0;
            correction_count += 1;
            if correction_count % 3 == 0 {
                led.toggle();
                info!("    跨定时器纠正中... 剩余: {}°", yaw_deviation as i32);
            }
        }
        
        info!("✅ 跨定时器角度纠正完成");
        led.set_low();
        Timer::after(Duration::from_secs(2)).await;

        info!("🎉 跨定时器配置测试完成 - 性能优异!");
        Timer::after(Duration::from_secs(3)).await;
    }
}

// 跨定时器配置的优势说明
/*
## 🎯 跨定时器配置的优势

### 1. 更大的引脚选择灵活性
- 不再受限于单个定时器的通道数量
- 可以使用任意可用的 PWM 引脚组合
- 避免引脚冲突和布线困难

### 2. 更好的资源利用
- 分散负载到多个定时器
- 避免单个定时器过载
- 提高系统整体性能

### 3. 降低干扰
- 不同定时器的 PWM 信号相互独立
- 减少电磁干扰
- 提高信号质量

### 4. 扩展性更强
- 易于添加更多电机
- 支持复杂的多电机系统
- 为未来功能预留资源

### 5. 故障隔离
- 单个定时器故障不会影响所有电机
- 更好的系统可靠性
- 便于调试和维护

## 📊 配置对比

| 配置方式 | 定时器使用 | 引脚灵活性 | 资源利用 | 扩展性 |
|----------|------------|------------|----------|--------|
| 传统方式 | 1-2个定时器 | 受限 | 集中 | 有限 |
| 跨定时器 | 4个定时器 | 最大 | 分散 | 优秀 |

## 🔧 实际应用场景

1. **复杂机器人**: 多个电机系统
2. **精密控制**: 需要独立调节的应用
3. **引脚受限**: PCB 布线困难的情况
4. **高性能**: 需要最大化利用硬件资源

这个新功能让 AT8236 驱动器在复杂应用中更加实用！
*/
