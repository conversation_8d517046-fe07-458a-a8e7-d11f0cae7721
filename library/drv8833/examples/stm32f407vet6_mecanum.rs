//! STM32F407VET6 麦克纳姆轮驱动示例 - 高性能版本
//!
//! 这个示例专门为 STM32F407VET6 设计，充分利用 F407 的强大功能：
//! - 更多的定时器和 PWM 通道
//! - 更高的时钟频率 (168MHz)
//! - 更多的 GPIO 引脚选择
//! - 支持 DRV8833 和 AT8236 驱动器
//!
//! # 硬件连接 (STM32F407VET6)
//!
//! ```text
//! F407VET6 引脚配置 (每个电机需要 2 个 PWM 引脚):
//! 
//! FL Motor (左前轮) - TIM1:
//! PE9  (TIM1_CH1) -> DRV8833/AT8236_FL_IN1
//! PE11 (TIM1_CH2) -> DRV8833/AT8236_FL_IN2
//! 
//! FR Motor (右前轮) - TIM1:
//! PE13 (TIM1_CH3) -> DRV8833/AT8236_FR_IN1
//! PE14 (TIM1_CH4) -> DRV8833/AT8236_FR_IN2
//! 
//! BL Motor (左后轮) - TIM3:
//! PC6  (TIM3_CH1) -> DRV8833/AT8236_BL_IN1
//! PC7  (TIM3_CH2) -> DRV8833/AT8236_BL_IN2
//! 
//! BR Motor (右后轮) - TIM3:
//! PC8  (TIM3_CH3) -> DRV8833/AT8236_BR_IN1
//! PC9  (TIM3_CH4) -> DRV8833/AT8236_BR_IN2
//! 
//! 电源和控制:
//! 3.3V -> 驱动器 VCC
//! GND  -> 驱动器 GND
//! PC13 -> LED (板载 LED)
//! ```

#![no_std]
#![no_main]

use defmt::*;
use defmt_rtt as _;
use embassy_executor::Spawner;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::{bind_interrupts, peripherals, timer, Config};
use embassy_time::{Duration, Timer};
use panic_probe as _;

// 选择驱动器类型 - 编译时切换
use drv8833::Drv8833Single;
// use drv8833::at8236::At8236Single;  // 取消注释以使用 AT8236

use drv8833::mecanum::{GenericMecanumDrive, MecanumDirection};

bind_interrupts!(struct Irqs {
    TIM1_UP_TIM10 => timer::InterruptHandler<peripherals::TIM1>;
    TIM3 => timer::InterruptHandler<peripherals::TIM3>;
});

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    // F407VET6 配置 - 启用高性能时钟
    let mut config = Config::default();
    config.rcc.hse = Some(Hertz(8_000_000)); // 外部 8MHz 晶振
    config.rcc.pll_src = embassy_stm32::rcc::PllSource::HSE;
    config.rcc.pll = Some(embassy_stm32::rcc::Pll {
        prediv: embassy_stm32::rcc::PllPreDiv::DIV4,
        mul: embassy_stm32::rcc::PllMul::MUL168,
        divp: Some(embassy_stm32::rcc::PllPDiv::DIV2), // 168MHz 系统时钟
        divq: Some(embassy_stm32::rcc::PllQDiv::DIV7),
        divr: None,
    });
    config.rcc.ahb_pre = embassy_stm32::rcc::AHBPrescaler::DIV1;
    config.rcc.apb1_pre = embassy_stm32::rcc::APBPrescaler::DIV4; // 42MHz
    config.rcc.apb2_pre = embassy_stm32::rcc::APBPrescaler::DIV2; // 84MHz

    let p = embassy_stm32::init(config);
    info!("🚀 STM32F407VET6 麦克纳姆轮驱动启动 (168MHz)");

    // 板载 LED 指示
    let mut led = Output::new(p.PC13, Level::High, Speed::Low);
    led.set_low(); // 点亮 LED 表示启动

    // 配置 PWM 引脚 - 使用 F407VET6 的高性能引脚
    // FL Motor (左前轮) - TIM1 CH1/CH2
    let fl_in1_pin = PwmPin::new_ch1(p.PE9, embassy_stm32::gpio::Pull::None);
    let fl_in2_pin = PwmPin::new_ch2(p.PE11, embassy_stm32::gpio::Pull::None);
    
    // FR Motor (右前轮) - TIM1 CH3/CH4
    let fr_in1_pin = PwmPin::new_ch3(p.PE13, embassy_stm32::gpio::Pull::None);
    let fr_in2_pin = PwmPin::new_ch4(p.PE14, embassy_stm32::gpio::Pull::None);
    
    // BL Motor (左后轮) - TIM3 CH1/CH2
    let bl_in1_pin = PwmPin::new_ch1(p.PC6, embassy_stm32::gpio::Pull::None);
    let bl_in2_pin = PwmPin::new_ch2(p.PC7, embassy_stm32::gpio::Pull::None);
    
    // BR Motor (右后轮) - TIM3 CH3/CH4
    let br_in1_pin = PwmPin::new_ch3(p.PC8, embassy_stm32::gpio::Pull::None);
    let br_in2_pin = PwmPin::new_ch4(p.PC9, embassy_stm32::gpio::Pull::None);

    info!("📌 F407VET6 PWM 引脚配置完成");

    // 创建高频 PWM 实例 - 利用 F407 的高性能
    let mut pwm_tim1 = SimplePwm::new(
        p.TIM1,
        Some(fl_in1_pin),  // CH1 - FL_IN1
        Some(fl_in2_pin),  // CH2 - FL_IN2
        Some(fr_in1_pin),  // CH3 - FR_IN1
        Some(fr_in2_pin),  // CH4 - FR_IN2
        Hertz(20_000),     // 20kHz 高频 PWM (F407 可以支持)
        Default::default(),
    );

    let mut pwm_tim3 = SimplePwm::new(
        p.TIM3,
        Some(bl_in1_pin),  // CH1 - BL_IN1
        Some(bl_in2_pin),  // CH2 - BL_IN2
        Some(br_in1_pin),  // CH3 - BR_IN1
        Some(br_in2_pin),  // CH4 - BR_IN2
        Hertz(20_000),     // 20kHz 高频 PWM
        Default::default(),
    );

    // 启用 PWM 输出
    pwm_tim1.enable_all();
    pwm_tim3.enable_all();

    info!("⚡ F407VET6 高频 PWM (20kHz) 启用完成");

    // 获取 PWM 通道
    let fl_pwm_in1 = pwm_tim1.ch1();
    let fl_pwm_in2 = pwm_tim1.ch2();
    let fr_pwm_in1 = pwm_tim1.ch3();
    let fr_pwm_in2 = pwm_tim1.ch4();
    let bl_pwm_in1 = pwm_tim3.ch1();
    let bl_pwm_in2 = pwm_tim3.ch2();
    let br_pwm_in1 = pwm_tim3.ch3();
    let br_pwm_in2 = pwm_tim3.ch4();

    // 创建电机驱动器 (可以选择 DRV8833 或 AT8236)
    let fl_motor = Drv8833Single::new(fl_pwm_in1, fl_pwm_in2);
    let fr_motor = Drv8833Single::new(fr_pwm_in1, fr_pwm_in2);
    let bl_motor = Drv8833Single::new(bl_pwm_in1, bl_pwm_in2);
    let br_motor = Drv8833Single::new(br_pwm_in1, br_pwm_in2);

    // 如果要使用 AT8236，取消注释下面的代码：
    // let fl_motor = At8236Single::new(fl_pwm_in1, fl_pwm_in2);
    // let fr_motor = At8236Single::new(fr_pwm_in1, fr_pwm_in2);
    // let bl_motor = At8236Single::new(bl_pwm_in1, bl_pwm_in2);
    // let br_motor = At8236Single::new(br_pwm_in1, br_pwm_in2);

    // 创建麦克纳姆驱动系统
    let mut mecanum = GenericMecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);

    info!("🎯 F407VET6 麦克纳姆驱动系统初始化完成");
    info!("📊 硬件配置: F407VET6 @ 168MHz, 4 电机, 8 PWM @ 20kHz");

    // LED 闪烁表示准备就绪
    for _ in 0..3 {
        led.set_high();
        Timer::after(Duration::from_millis(100)).await;
        led.set_low();
        Timer::after(Duration::from_millis(100)).await;
    }

    // 高性能测试序列
    info!("🔧 开始 F407VET6 高性能测试序列...");

    loop {
        // 高速运动测试 - 利用 F407 的性能
        info!("🚀 高速前进 (F407VET6)");
        let _ = mecanum.move_direction(MecanumDirection::Forward, 70);
        led.set_high();
        Timer::after(Duration::from_secs(2)).await;

        info!("🚀 高速后退 (F407VET6)");
        let _ = mecanum.move_direction(MecanumDirection::Backward, 70);
        Timer::after(Duration::from_secs(2)).await;

        info!("🚀 高速左平移 (F407VET6)");
        let _ = mecanum.move_direction(MecanumDirection::Left, 70);
        Timer::after(Duration::from_secs(2)).await;

        info!("🚀 高速右平移 (F407VET6)");
        let _ = mecanum.move_direction(MecanumDirection::Right, 70);
        Timer::after(Duration::from_secs(2)).await;

        info!("🚀 高速顺时针旋转 (F407VET6)");
        let _ = mecanum.move_direction(MecanumDirection::RotateClockwise, 70);
        Timer::after(Duration::from_secs(2)).await;

        info!("🚀 高速逆时针旋转 (F407VET6)");
        let _ = mecanum.move_direction(MecanumDirection::RotateCounterClockwise, 70);
        Timer::after(Duration::from_secs(2)).await;

        info!("⏸️ 停止");
        let _ = mecanum.stop_all();
        led.set_low();
        Timer::after(Duration::from_secs(1)).await;

        // 精确角度纠正测试 - 利用高频 PWM 的精度
        info!("🎯 F407VET6 精确角度纠正测试");
        let mut yaw_deviation = 5.0; // 更小的偏差测试
        let mut correction_count = 0;
        
        while !mecanum.correct_yaw_deviation(yaw_deviation, 50) && correction_count < 10 {
            Timer::after(Duration::from_millis(50)).await; // 更快的响应
            yaw_deviation -= 0.5; // 更精细的纠正
            correction_count += 1;
            if correction_count % 2 == 0 {
                led.toggle();
                info!("  F407 精确纠正... 剩余: {:.1}°", yaw_deviation);
            }
        }
        
        info!("✅ F407VET6 精确角度纠正完成");
        led.set_low();
        Timer::after(Duration::from_secs(2)).await;

        // 渐变速度测试 - 展示平滑控制
        info!("🌊 F407VET6 渐变速度测试");
        for speed in (10..=80).step_by(10) {
            info!("  速度: {}%", speed);
            let _ = mecanum.move_direction(MecanumDirection::Forward, speed);
            led.toggle();
            Timer::after(Duration::from_millis(500)).await;
        }
        
        // 渐减速度
        for speed in (10..=80).step_by(10).rev() {
            info!("  减速: {}%", speed);
            let _ = mecanum.move_direction(MecanumDirection::Forward, speed);
            led.toggle();
            Timer::after(Duration::from_millis(500)).await;
        }
        
        let _ = mecanum.stop_all();
        info!("✅ 渐变速度测试完成");
        Timer::after(Duration::from_secs(2)).await;
    }
}
