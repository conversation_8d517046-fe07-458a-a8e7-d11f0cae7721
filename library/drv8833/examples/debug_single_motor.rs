//! 单电机调试示例 - F407VET6

#![no_std]
#![no_main]

use defmt::*;
use defmt_rtt as _;
use embassy_executor::Spawner;
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::{bind_interrupts, peripherals, timer, Config};
use embassy_time::{Duration, Timer};
use panic_probe as _;

use drv8833::{Direction, Drv8833Single};

bind_interrupts!(struct Irqs {
    TIM1_UP_TIM10 => timer::InterruptHandler<peripherals::TIM1>;
});

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());
    info!("🔧 F407VET6 单电机调试启动");

    // 使用 PE9/PE11 (TIM1_CH1/CH2)
    let in1_pin = PwmPin::new_ch1(p.PE9, embassy_stm32::gpio::Pull::None);
    let in2_pin = PwmPin::new_ch2(p.PE11, embassy_stm32::gpio::Pull::None);

    let mut pwm = SimplePwm::new(
        p.TIM1,
        Some(in1_pin),
        Some(in2_pin),
        None,
        None,
        Hertz(20_000), // 20kHz PWM
        Default::default(),
    );

    pwm.enable_all();
    info!("⚡ PWM 启用完成");

    let pwm_in1 = pwm.ch1();
    let pwm_in2 = pwm.ch2();
    let mut motor = Drv8833Single::new(pwm_in1, pwm_in2);

    loop {
        info!("🔄 测试序列开始");

        // 低速前进
        info!("  前进 20%");
        match motor.set_motor(Direction::Forward, 20) {
            Ok(_) => info!("    ✅ 成功"),
            Err(e) => error!("    ❌ 失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 停止
        info!("  停止");
        let _ = motor.coast();
        Timer::after(Duration::from_secs(1)).await;

        // 低速后退
        info!("  后退 20%");
        match motor.set_motor(Direction::Backward, 20) {
            Ok(_) => info!("    ✅ 成功"),
            Err(e) => error!("    ❌ 失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 刹车
        info!("  刹车");
        let _ = motor.brake();
        Timer::after(Duration::from_secs(1)).await;

        info!("✅ 测试序列完成");
        Timer::after(Duration::from_secs(2)).await;
    }
}
