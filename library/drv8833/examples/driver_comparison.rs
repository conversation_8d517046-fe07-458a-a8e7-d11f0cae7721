//! 驱动器对比示例 - DRV8833 vs AT8236

#![no_std]
#![no_main]

use defmt::*;
use defmt_rtt as _;
use embassy_executor::Spawner;
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::{bind_interrupts, peripherals, timer, Config};
use embassy_time::{Duration, Timer};
use panic_probe as _;

use drv8833::Drv8833Single;
use drv8833::at8236::At8236Single;
use drv8833::mecanum::{GenericMecanumDrive, MecanumDirection};

bind_interrupts!(struct Irqs {
    TIM1_UP_TIM10 => timer::InterruptHandler<peripherals::TIM1>;
    TIM3 => timer::InterruptHandler<peripherals::TIM3>;
});

// 选择驱动器类型
const USE_AT8236: bool = false; // true = AT8236, false = DRV8833

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());
    
    if USE_AT8236 {
        info!("🚀 使用 AT8236 驱动器");
    } else {
        info!("🚀 使用 DRV8833 驱动器");
    }

    // PWM 配置 (两种驱动器使用相同配置)
    let fl_in1_pin = PwmPin::new_ch1(p.PE9, embassy_stm32::gpio::Pull::None);
    let fl_in2_pin = PwmPin::new_ch2(p.PE11, embassy_stm32::gpio::Pull::None);
    let fr_in1_pin = PwmPin::new_ch3(p.PE13, embassy_stm32::gpio::Pull::None);
    let fr_in2_pin = PwmPin::new_ch4(p.PE14, embassy_stm32::gpio::Pull::None);
    let bl_in1_pin = PwmPin::new_ch1(p.PC6, embassy_stm32::gpio::Pull::None);
    let bl_in2_pin = PwmPin::new_ch2(p.PC7, embassy_stm32::gpio::Pull::None);
    let br_in1_pin = PwmPin::new_ch3(p.PC8, embassy_stm32::gpio::Pull::None);
    let br_in2_pin = PwmPin::new_ch4(p.PC9, embassy_stm32::gpio::Pull::None);

    let mut pwm_tim1 = SimplePwm::new(
        p.TIM1,
        Some(fl_in1_pin),
        Some(fl_in2_pin),
        Some(fr_in1_pin),
        Some(fr_in2_pin),
        Hertz(20_000),
        Default::default(),
    );

    let mut pwm_tim3 = SimplePwm::new(
        p.TIM3,
        Some(bl_in1_pin),
        Some(bl_in2_pin),
        Some(br_in1_pin),
        Some(br_in2_pin),
        Hertz(20_000),
        Default::default(),
    );

    pwm_tim1.enable_all();
    pwm_tim3.enable_all();

    // 根据选择创建不同的驱动器
    if USE_AT8236 {
        // 使用 AT8236
        let fl_motor = At8236Single::new(pwm_tim1.ch1(), pwm_tim1.ch2());
        let fr_motor = At8236Single::new(pwm_tim1.ch3(), pwm_tim1.ch4());
        let bl_motor = At8236Single::new(pwm_tim3.ch1(), pwm_tim3.ch2());
        let br_motor = At8236Single::new(pwm_tim3.ch3(), pwm_tim3.ch4());

        let mut mecanum = GenericMecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);
        info!("✅ AT8236 系统初始化完成");
        run_test(&mut mecanum, "AT8236").await;
    } else {
        // 使用 DRV8833
        let fl_motor = Drv8833Single::new(pwm_tim1.ch1(), pwm_tim1.ch2());
        let fr_motor = Drv8833Single::new(pwm_tim1.ch3(), pwm_tim1.ch4());
        let bl_motor = Drv8833Single::new(pwm_tim3.ch1(), pwm_tim3.ch2());
        let br_motor = Drv8833Single::new(pwm_tim3.ch3(), pwm_tim3.ch4());

        let mut mecanum = GenericMecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);
        info!("✅ DRV8833 系统初始化完成");
        run_test(&mut mecanum, "DRV8833").await;
    }
}

async fn run_test<FL, FR, BL, BR>(
    mecanum: &mut GenericMecanumDrive<FL, FR, BL, BR>,
    driver_name: &str,
) where
    FL: drv8833::MotorDriver,
    FR: drv8833::MotorDriver,
    BL: drv8833::MotorDriver,
    BR: drv8833::MotorDriver,
{
    loop {
        info!("🔄 {} - 前进", driver_name);
        let _ = mecanum.move_direction(MecanumDirection::Forward, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("🔄 {} - 后退", driver_name);
        let _ = mecanum.move_direction(MecanumDirection::Backward, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("⏸️ {} - 停止", driver_name);
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;
    }
}
