#![no_std]

//! DRV8833 双 H 桥电机驱动器 - 正确实现
//!
//! **重要**: DRV8833 没有单独的 PWM 引脚！
//! 速度控制是通过在方向引脚上施加 PWM 信号实现的。
//!
//! # DRV8833 真实工作原理
//!
//! ```text
//! 电机控制方式:
//! - 前进: IN1=PWM(speed), IN2=0%
//! - 后退: IN1=0%, IN2=PWM(speed)  
//! - 刹车: IN1=100%, IN2=100%
//! - 滑行: IN1=0%, IN2=0%
//! ```
//!
//! # 硬件连接
//!
//! 每个电机需要 2 个 PWM 引脚：
//! ```text
//! STM32    DRV8833    Motor
//! PA0  ->  AIN1   ->   M1+
//! PA1  ->  AIN2   ->   M1-
//! PA2  ->  BIN1   ->   M2+
//! PA3  ->  BIN2   ->   M2-
//! ```
//!
//! # 库结构
//!
//! - `Drv8833Single`: 控制一个电机 (需要 2 个 PWM 引脚)
//! - `mecanum::MecanumDrive`: 使用 4 个 Drv8833Single 实现麦克纳姆驱动

use embassy_stm32::timer::GeneralInstance4Channel;
use embassy_stm32::timer::simple_pwm::SimplePwmChannel;

/// DRV8833 驱动错误
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[cfg_attr(feature = "defmt", derive(defmt::Format))]
pub enum Error {
    /// 无效的速度值 (必须在 0-100 之间)
    InvalidSpeed,
}

/// 电机方向
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Direction {
    /// 前进
    Forward,
    /// 后退
    Backward,
    /// 刹车 (两引脚都为高电平)
    Brake,
    /// 滑行 (两引脚都为低电平)
    Coast,
}

/// DRV8833 单电机驱动器 - 正确实现
///
/// 这个结构体正确地实现了 DRV8833 的控制方式：
/// - 使用两个 PWM 通道 (IN1, IN2)
/// - 通过 PWM 占空比控制速度和方向
///
/// # 控制原理
/// ```text
/// 前进: IN1=PWM(speed), IN2=0%
/// 后退: IN1=0%, IN2=PWM(speed)
/// 刹车: IN1=100%, IN2=100%
/// 滑行: IN1=0%, IN2=0%
/// ```
///
/// # 用法
/// ```rust,no_run
/// let motor = Drv8833Single::new(pwm_in1, pwm_in2);
/// motor.set_motor(Direction::Forward, 50)?; // 前进 50% 速度
/// motor.set_motor(Direction::Backward, 75)?; // 后退 75% 速度
/// motor.brake()?; // 刹车
/// motor.coast()?; // 滑行
/// ```
pub struct Drv8833Single<TIM: GeneralInstance4Channel> {
    in1: SimplePwmChannel<'static, TIM>,
    in2: SimplePwmChannel<'static, TIM>,
}

impl<TIM: GeneralInstance4Channel> Drv8833Single<TIM> {
    /// 创建新的 DRV8833 单电机驱动器
    ///
    /// # 参数
    /// * `in1` - 电机 IN1 引脚的 PWM 通道
    /// * `in2` - 电机 IN2 引脚的 PWM 通道
    pub fn new(in1: SimplePwmChannel<'static, TIM>, in2: SimplePwmChannel<'static, TIM>) -> Self {
        Self { in1, in2 }
    }

    /// 设置电机方向和速度
    ///
    /// # 参数
    /// * `direction` - 电机方向
    /// * `speed` - 速度百分比 (0-100)
    pub fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), Error> {
        if speed > 100 {
            return Err(Error::InvalidSpeed);
        }

        match direction {
            Direction::Forward => {
                let max_duty = self.in1.max_duty_cycle();
                let duty = (max_duty as u32 * speed as u32) / 100;
                self.in1.set_duty_cycle(duty as u16);
                self.in2.set_duty_cycle(0);
            }
            Direction::Backward => {
                let max_duty = self.in2.max_duty_cycle();
                let duty = (max_duty as u32 * speed as u32) / 100;
                self.in1.set_duty_cycle(0);
                self.in2.set_duty_cycle(duty as u16);
            }
            Direction::Brake => {
                let max_duty = self.in1.max_duty_cycle();
                self.in1.set_duty_cycle(max_duty);
                self.in2.set_duty_cycle(max_duty);
            }
            Direction::Coast => {
                self.in1.set_duty_cycle(0);
                self.in2.set_duty_cycle(0);
            }
        }

        Ok(())
    }

    /// 刹车电机
    pub fn brake(&mut self) -> Result<(), Error> {
        self.set_motor(Direction::Brake, 0)
    }

    /// 滑行停止电机
    pub fn coast(&mut self) -> Result<(), Error> {
        self.set_motor(Direction::Coast, 0)
    }

    /// 获取 IN1 PWM 通道的引用
    pub fn in1_pwm(&mut self) -> &mut SimplePwmChannel<'static, TIM> {
        &mut self.in1
    }

    /// 获取 IN2 PWM 通道的引用
    pub fn in2_pwm(&mut self) -> &mut SimplePwmChannel<'static, TIM> {
        &mut self.in2
    }
}

/// 通用电机驱动 trait
///
/// 这个 trait 定义了电机驱动器的通用接口，
/// 允许麦克纳姆轮库同时支持不同的驱动芯片 (DRV8833, AT8236 等)
pub trait MotorDriver {
    /// 错误类型
    type Error;

    /// 设置电机方向和速度
    ///
    /// # 参数
    /// * `direction` - 电机方向
    /// * `speed` - 速度百分比 (0-100)
    fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), Self::Error>;

    /// 刹车电机
    fn brake(&mut self) -> Result<(), Self::Error>;

    /// 滑行停止电机
    fn coast(&mut self) -> Result<(), Self::Error>;
}

// 为 Drv8833Single 实现 MotorDriver trait
impl<TIM: GeneralInstance4Channel> MotorDriver for Drv8833Single<TIM> {
    type Error = Error;

    fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), Self::Error> {
        self.set_motor(direction, speed)
    }

    fn brake(&mut self) -> Result<(), Self::Error> {
        self.brake()
    }

    fn coast(&mut self) -> Result<(), Self::Error> {
        self.coast()
    }
}

pub mod at8236;
pub mod mecanum;
