//! # AB-Phase Hall Encoder Driver
//!
//! This module provides a high-performance driver for AB-phase (quadrature) Hall encoders.
//! It supports multiple decoding modes, direction detection, and real-time position/velocity calculation.
//!
//! ## Features
//!
//! - **Quadrature decoding**: 1x, 2x, 4x precision modes
//! - **Direction detection**: Automatic CW/CCW detection
//! - **Position tracking**: 32-bit signed position counter
//! - **Velocity calculation**: Real-time speed measurement
//! - **Embassy integration**: Async GPIO interrupt support
//! - **Error handling**: Signal validation and noise detection
//!
//! ## Usage
//!
//! ```rust,no_run
//! use motor_encoder::encoder::{QuadratureEncoder, EncoderConfig, DecodingMode};
//! use embassy_stm32::gpio::{Input, Pull};
//!
//! // Initialize encoder pins
//! let pin_a = Input::new(p.PA0, Pull::Up);
//! let pin_b = Input::new(p.PA1, Pull::Up);
//!
//! // Create encoder configuration
//! let config = EncoderConfig::new()
//!     .with_decoding_mode(DecodingMode::X4)
//!     .with_pulses_per_revolution(1000);
//!
//! // Create encoder instance
//! let mut encoder = QuadratureEncoder::new(pin_a, pin_b, config);
//!
//! // Read position and velocity
//! let position = encoder.position();
//! let velocity = encoder.velocity_rpm();
//! ```

use core::sync::atomic::{AtomicI32, AtomicU32, Ordering};

#[cfg(feature = "embassy")]
use embassy_time::Instant;

/// Quadrature encoder decoding precision modes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DecodingMode {
    /// 1x decoding - count on A channel rising edge only
    X1,
    /// 2x decoding - count on both A channel edges
    X2,
    /// 4x decoding - count on all A and B channel edges (full quadrature)
    X4,
}

impl Default for DecodingMode {
    fn default() -> Self {
        DecodingMode::X4
    }
}

/// Rotation direction
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Direction {
    /// Clockwise rotation (positive direction)
    Clockwise,
    /// Counter-clockwise rotation (negative direction)
    CounterClockwise,
    /// No movement detected
    Stopped,
}

impl Default for Direction {
    fn default() -> Self {
        Direction::Stopped
    }
}

/// Encoder error types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum EncoderError {
    /// Invalid state transition detected (possible noise or signal loss)
    InvalidTransition,
    /// Signal timeout - no edges detected within expected timeframe
    SignalTimeout,
    /// Configuration error
    InvalidConfiguration,
    /// GPIO error
    GpioError,
}

/// Encoder configuration
#[derive(Debug, Clone)]
pub struct EncoderConfig {
    /// Decoding mode (1x, 2x, or 4x)
    pub decoding_mode: DecodingMode,
    /// Number of encoder pulses per full revolution
    pub pulses_per_revolution: u32,
    /// Velocity calculation window in milliseconds
    pub velocity_window_ms: u32,
    /// Maximum time between edges before considering stopped (ms)
    pub timeout_ms: u32,
    /// Enable error checking for invalid state transitions
    pub enable_error_checking: bool,
}

impl Default for EncoderConfig {
    fn default() -> Self {
        Self {
            decoding_mode: DecodingMode::X4,
            pulses_per_revolution: 1000,
            velocity_window_ms: 100,
            timeout_ms: 1000,
            enable_error_checking: true,
        }
    }
}

impl EncoderConfig {
    /// Create a new encoder configuration with default values
    pub fn new() -> Self {
        Self::default()
    }

    /// Set the decoding mode
    pub fn with_decoding_mode(mut self, mode: DecodingMode) -> Self {
        self.decoding_mode = mode;
        self
    }

    /// Set the number of pulses per revolution
    pub fn with_pulses_per_revolution(mut self, ppr: u32) -> Self {
        self.pulses_per_revolution = ppr;
        self
    }

    /// Set the velocity calculation window
    pub fn with_velocity_window_ms(mut self, window_ms: u32) -> Self {
        self.velocity_window_ms = window_ms;
        self
    }

    /// Set the signal timeout
    pub fn with_timeout_ms(mut self, timeout_ms: u32) -> Self {
        self.timeout_ms = timeout_ms;
        self
    }

    /// Enable or disable error checking
    pub fn with_error_checking(mut self, enable: bool) -> Self {
        self.enable_error_checking = enable;
        self
    }
}

/// Encoder state for quadrature decoding
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
struct EncoderState {
    /// Current A and B pin states (bit 1: A, bit 0: B)
    current: u8,
    /// Previous A and B pin states
    previous: u8,
}

impl EncoderState {
    fn new() -> Self {
        Self {
            current: 0,
            previous: 0,
        }
    }

    /// Update state with new pin readings
    fn update(&mut self, pin_a: bool, pin_b: bool) {
        self.previous = self.current;
        self.current = ((pin_a as u8) << 1) | (pin_b as u8);
    }

    /// Get the current state as a 2-bit value
    fn current_state(&self) -> u8 {
        self.current
    }

    /// Get the previous state as a 2-bit value
    fn previous_state(&self) -> u8 {
        self.previous
    }

    /// Check if state has changed
    fn has_changed(&self) -> bool {
        self.current != self.previous
    }
}

/// Velocity measurement data
#[derive(Debug, Clone, Copy)]
struct VelocityData {
    /// Last edge timestamp
    #[cfg(feature = "embassy")]
    last_edge_time: Option<Instant>,
    /// Position at last velocity calculation
    last_position: i32,
    /// Calculated velocity in counts per second
    velocity_cps: f32,
    /// Current direction
    direction: Direction,
}

impl Default for VelocityData {
    fn default() -> Self {
        Self {
            #[cfg(feature = "embassy")]
            last_edge_time: None,
            last_position: 0,
            velocity_cps: 0.0,
            direction: Direction::Stopped,
        }
    }
}

/// Quadrature encoder state machine lookup table for 4x decoding
///
/// This table maps state transitions to position increments:
/// - Positive values indicate clockwise rotation
/// - Negative values indicate counter-clockwise rotation
/// - Zero indicates invalid transition or no change
const QUADRATURE_TABLE: [i8; 16] = [
    //        Previous state (bits 3:2) | Current state (bits 1:0)
    //        00  01  10  11
    /* 00 */
    0, 1, -1, 0, // From 00
    /* 01 */ -1, 0, 0, 1, // From 01
    /* 10 */ 1, 0, 0, -1, // From 10
    /* 11 */ 0, -1, 1, 0, // From 11
];

/// Get position increment from state transition for 4x decoding
fn get_quadrature_increment(previous: u8, current: u8) -> i8 {
    let index = ((previous & 0x03) << 2) | (current & 0x03);
    QUADRATURE_TABLE[index as usize]
}

/// Validate state transition for error detection
fn is_valid_transition(previous: u8, current: u8) -> bool {
    // In quadrature encoding, only one bit should change at a time
    let diff = previous ^ current;
    diff.count_ones() <= 1
}

/// Main quadrature encoder driver
pub struct QuadratureEncoder<PinA, PinB> {
    /// A-phase input pin
    pin_a: PinA,
    /// B-phase input pin
    pin_b: PinB,
    /// Encoder configuration
    config: EncoderConfig,
    /// Current encoder state
    state: EncoderState,
    /// Atomic position counter (thread-safe for interrupt access)
    position: AtomicI32,
    /// Velocity measurement data
    velocity_data: VelocityData,
    /// Error counter
    error_count: AtomicU32,
}

impl<PinA, PinB> QuadratureEncoder<PinA, PinB>
where
    PinA: embedded_hal::digital::InputPin,
    PinB: embedded_hal::digital::InputPin,
{
    /// Create a new quadrature encoder instance
    pub fn new(pin_a: PinA, pin_b: PinB, config: EncoderConfig) -> Self {
        let mut encoder = Self {
            pin_a,
            pin_b,
            config,
            state: EncoderState::new(),
            position: AtomicI32::new(0),
            velocity_data: VelocityData::default(),
            error_count: AtomicU32::new(0),
        };

        // Initialize state with current pin readings
        encoder.update_state().ok();

        encoder
    }

    /// Update encoder state by reading current pin values
    pub fn update_state(&mut self) -> Result<(), EncoderError> {
        let pin_a_state = self.pin_a.is_high().map_err(|_| EncoderError::GpioError)?;
        let pin_b_state = self.pin_b.is_high().map_err(|_| EncoderError::GpioError)?;

        self.state.update(pin_a_state, pin_b_state);
        Ok(())
    }

    /// Process encoder state change and update position
    /// This should be called from interrupt handlers for best performance
    pub fn process_edge(&mut self) -> Result<(), EncoderError> {
        // Update current state
        self.update_state()?;

        // Check if state actually changed
        if !self.state.has_changed() {
            return Ok(());
        }

        // Validate transition if error checking is enabled
        if self.config.enable_error_checking {
            if !is_valid_transition(self.state.previous_state(), self.state.current_state()) {
                self.error_count.fetch_add(1, Ordering::Relaxed);
                return Err(EncoderError::InvalidTransition);
            }
        }

        // Calculate position increment based on decoding mode
        let increment = match self.config.decoding_mode {
            DecodingMode::X1 => self.process_x1_decoding(),
            DecodingMode::X2 => self.process_x2_decoding(),
            DecodingMode::X4 => self.process_x4_decoding(),
        };

        // Update position atomically
        if increment != 0 {
            self.position.fetch_add(increment as i32, Ordering::Relaxed);
            self.update_velocity(increment);
        }

        Ok(())
    }

    /// Process 1x decoding (count on A channel rising edge only)
    fn process_x1_decoding(&self) -> i8 {
        let prev_a = (self.state.previous_state() & 0x02) != 0;
        let curr_a = (self.state.current_state() & 0x02) != 0;
        let curr_b = (self.state.current_state() & 0x01) != 0;

        // Count only on A rising edge
        if !prev_a && curr_a {
            if curr_b {
                -1 // Counter-clockwise
            } else {
                1 // Clockwise
            }
        } else {
            0
        }
    }

    /// Process 2x decoding (count on both A channel edges)
    fn process_x2_decoding(&self) -> i8 {
        let prev_a = (self.state.previous_state() & 0x02) != 0;
        let curr_a = (self.state.current_state() & 0x02) != 0;
        let curr_b = (self.state.current_state() & 0x01) != 0;

        // Count on both A edges
        if prev_a != curr_a {
            if curr_a == curr_b {
                -1 // Counter-clockwise
            } else {
                1 // Clockwise
            }
        } else {
            0
        }
    }

    /// Process 4x decoding (count on all edges - full quadrature)
    fn process_x4_decoding(&self) -> i8 {
        get_quadrature_increment(self.state.previous_state(), self.state.current_state())
    }

    /// Update velocity calculation
    fn update_velocity(&mut self, increment: i8) {
        #[cfg(feature = "embassy")]
        {
            let now = Instant::now();

            if let Some(last_time) = self.velocity_data.last_edge_time {
                let time_diff = now.duration_since(last_time);
                if time_diff.as_millis() > 0 {
                    // Calculate velocity in counts per second
                    let velocity_cps = (increment as f32) / (time_diff.as_millis() as f32 / 1000.0);
                    self.velocity_data.velocity_cps = velocity_cps;

                    // Update direction
                    self.velocity_data.direction = if increment > 0 {
                        Direction::Clockwise
                    } else if increment < 0 {
                        Direction::CounterClockwise
                    } else {
                        Direction::Stopped
                    };
                }
            }

            self.velocity_data.last_edge_time = Some(now);
        }

        #[cfg(not(feature = "embassy"))]
        {
            // Simple direction tracking without timing
            self.velocity_data.direction = if increment > 0 {
                Direction::Clockwise
            } else if increment < 0 {
                Direction::CounterClockwise
            } else {
                Direction::Stopped
            };
        }
    }

    /// Get current position in encoder counts
    pub fn position(&self) -> i32 {
        self.position.load(Ordering::Relaxed)
    }

    /// Reset position to zero
    pub fn reset_position(&self) {
        self.position.store(0, Ordering::Relaxed);
    }

    /// Set position to a specific value
    pub fn set_position(&self, position: i32) {
        self.position.store(position, Ordering::Relaxed);
    }

    /// Get current direction
    pub fn direction(&self) -> Direction {
        self.velocity_data.direction
    }

    /// Get velocity in counts per second
    #[cfg(feature = "embassy")]
    pub fn velocity_cps(&self) -> f32 {
        // Check for timeout
        if let Some(last_time) = self.velocity_data.last_edge_time {
            let now = Instant::now();
            if now.duration_since(last_time).as_millis() > self.config.timeout_ms as u64 {
                return 0.0; // Stopped due to timeout
            }
        }
        self.velocity_data.velocity_cps
    }

    /// Get velocity in RPM (revolutions per minute)
    #[cfg(feature = "embassy")]
    pub fn velocity_rpm(&self) -> f32 {
        let cps = self.velocity_cps();
        let counts_per_rev = match self.config.decoding_mode {
            DecodingMode::X1 => self.config.pulses_per_revolution,
            DecodingMode::X2 => self.config.pulses_per_revolution * 2,
            DecodingMode::X4 => self.config.pulses_per_revolution * 4,
        };
        (cps * 60.0) / counts_per_rev as f32
    }

    /// Get position in revolutions
    pub fn position_revolutions(&self) -> f32 {
        let position = self.position() as f32;
        let counts_per_rev = match self.config.decoding_mode {
            DecodingMode::X1 => self.config.pulses_per_revolution,
            DecodingMode::X2 => self.config.pulses_per_revolution * 2,
            DecodingMode::X4 => self.config.pulses_per_revolution * 4,
        };
        position / counts_per_rev as f32
    }

    /// Get current encoder state (for debugging)
    pub fn current_state(&self) -> u8 {
        self.state.current_state()
    }

    /// Get error count
    pub fn error_count(&self) -> u32 {
        self.error_count.load(Ordering::Relaxed)
    }

    /// Reset error count
    pub fn reset_error_count(&self) {
        self.error_count.store(0, Ordering::Relaxed);
    }

    /// Get encoder configuration
    pub fn config(&self) -> &EncoderConfig {
        &self.config
    }
}

#[cfg(feature = "embassy")]
pub mod embassy_support {
    //! Embassy-specific encoder implementations with async GPIO interrupt support

    use super::*;
    use core::cell::RefCell;
    use embassy_stm32::exti::ExtiInput;
    use embassy_stm32::gpio::{Input, Pull};
    use embassy_sync::blocking_mutex::Mutex;
    use embassy_sync::blocking_mutex::raw::CriticalSectionRawMutex;
    use embassy_time::{Duration, Timer};

    /// Embassy-specific quadrature encoder with async interrupt support
    pub struct EmbassyQuadratureEncoder {
        /// Encoder core logic
        encoder: Mutex<
            CriticalSectionRawMutex,
            RefCell<QuadratureEncoder<Input<'static>, Input<'static>>>,
        >,
        /// A-phase interrupt input
        pin_a_exti: ExtiInput<'static>,
        /// B-phase interrupt input
        pin_b_exti: ExtiInput<'static>,
    }

    impl EmbassyQuadratureEncoder {
        /// Create a new Embassy quadrature encoder
        pub fn new(
            pin_a: Input<'static>,
            pin_b: Input<'static>,
            pin_a_exti: ExtiInput<'static>,
            pin_b_exti: ExtiInput<'static>,
            config: EncoderConfig,
        ) -> Self {
            let encoder = QuadratureEncoder::new(pin_a, pin_b, config);

            Self {
                encoder: Mutex::new(RefCell::new(encoder)),
                pin_a_exti,
                pin_b_exti,
            }
        }

        /// Start the encoder with async interrupt handling
        /// This method will run indefinitely, processing encoder interrupts
        pub async fn run(&mut self) -> ! {
            loop {
                // Wait for either A or B phase edge
                embassy_futures::select::select(
                    self.pin_a_exti.wait_for_any_edge(),
                    self.pin_b_exti.wait_for_any_edge(),
                )
                .await;

                // Process the edge in critical section
                self.encoder.lock(|encoder_cell| {
                    let mut encoder = encoder_cell.borrow_mut();
                    let _ = encoder.process_edge();
                });
            }
        }

        /// Get current position (thread-safe)
        pub fn position(&self) -> i32 {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().position())
        }

        /// Reset position to zero (thread-safe)
        pub fn reset_position(&self) {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().reset_position())
        }

        /// Set position to specific value (thread-safe)
        pub fn set_position(&self, position: i32) {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().set_position(position))
        }

        /// Get current direction (thread-safe)
        pub fn direction(&self) -> Direction {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().direction())
        }

        /// Get velocity in counts per second (thread-safe)
        pub fn velocity_cps(&self) -> f32 {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().velocity_cps())
        }

        /// Get velocity in RPM (thread-safe)
        pub fn velocity_rpm(&self) -> f32 {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().velocity_rpm())
        }

        /// Get position in revolutions (thread-safe)
        pub fn position_revolutions(&self) -> f32 {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().position_revolutions())
        }

        /// Get error count (thread-safe)
        pub fn error_count(&self) -> u32 {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().error_count())
        }

        /// Reset error count (thread-safe)
        pub fn reset_error_count(&self) {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().reset_error_count())
        }

        /// Get encoder configuration (thread-safe)
        pub fn config(&self) -> EncoderConfig {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().config().clone())
        }
    }

    /// Simplified encoder for basic usage without EXTI interrupts
    /// This version uses polling instead of interrupts
    pub struct SimpleEmbassyEncoder {
        encoder: Mutex<
            CriticalSectionRawMutex,
            RefCell<QuadratureEncoder<Input<'static>, Input<'static>>>,
        >,
    }

    impl SimpleEmbassyEncoder {
        /// Create a new simple Embassy encoder (polling-based)
        pub fn new(pin_a: Input<'static>, pin_b: Input<'static>, config: EncoderConfig) -> Self {
            let encoder = QuadratureEncoder::new(pin_a, pin_b, config);
            Self {
                encoder: Mutex::new(RefCell::new(encoder)),
            }
        }

        /// Poll the encoder state (call this regularly from your main loop)
        pub fn poll(&self) -> Result<(), EncoderError> {
            self.encoder.lock(|encoder_cell| {
                let mut encoder = encoder_cell.borrow_mut();
                encoder.process_edge()
            })
        }

        /// Get current position (thread-safe)
        pub fn position(&self) -> i32 {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().position())
        }

        /// Reset position to zero (thread-safe)
        pub fn reset_position(&self) {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().reset_position())
        }

        /// Set position to specific value (thread-safe)
        pub fn set_position(&self, position: i32) {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().set_position(position))
        }

        /// Get current direction (thread-safe)
        pub fn direction(&self) -> Direction {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().direction())
        }

        /// Get velocity in counts per second (thread-safe)
        pub fn velocity_cps(&self) -> f32 {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().velocity_cps())
        }

        /// Get velocity in RPM (thread-safe)
        pub fn velocity_rpm(&self) -> f32 {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().velocity_rpm())
        }

        /// Get position in revolutions (thread-safe)
        pub fn position_revolutions(&self) -> f32 {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().position_revolutions())
        }

        /// Get error count (thread-safe)
        pub fn error_count(&self) -> u32 {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().error_count())
        }

        /// Reset error count (thread-safe)
        pub fn reset_error_count(&self) {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().reset_error_count())
        }

        /// Get encoder configuration (thread-safe)
        pub fn config(&self) -> EncoderConfig {
            self.encoder
                .lock(|encoder_cell| encoder_cell.borrow().config().clone())
        }
    }
}
