#![no_std]

//! # Motor Encoder Library
//!
//! A comprehensive library for motor control and encoder feedback in embedded systems.
//! This library provides drivers for:
//!
//! - AB-phase Hall encoders (quadrature encoders)
//! - Motor control integration
//! - Embassy async framework support
//!
//! ## Features
//!
//! - **High-performance quadrature decoding** with 1x, 2x, 4x precision
//! - **Direction detection** based on phase relationships
//! - **Position and velocity calculation** with real-time updates
//! - **Embassy integration** with async GPIO interrupts
//! - **Error handling** for signal loss and noise detection
//! - **Zero-cost abstractions** for optimal performance

pub mod encoder;
