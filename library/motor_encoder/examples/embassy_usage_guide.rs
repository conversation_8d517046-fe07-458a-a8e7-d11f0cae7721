//! Embassy SimplePwmChannel 使用指南
//! 
//! 这个示例展示了如何在 Embassy 项目中使用 DRV8833 驱动和 SimplePwmChannel。
//! 
//! 注意：这是一个代码示例，展示了正确的 API 使用方式。
//! 实际使用时需要根据你的硬件配置调整引脚和定时器设置。

use drv8833::{Direction, Drv8833, Motor, Drv8833Single};

// 这个示例展示了如何在 Embassy 中使用 DRV8833 驱动
// 以下代码展示了正确的 API 使用模式

fn embassy_dual_motor_example() {
    // 在实际的 Embassy 项目中，你会这样使用：
    
    /*
    use embassy_stm32::gpio::{Level, Output, Speed};
    use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
    use embassy_stm32::time::Hertz;
    use embassy_stm32::{bind_interrupts, peripherals, timer, Config};
    use embassy_time::{Duration, Timer};
    
    #[embassy_executor::main]
    async fn main(_spawner: Spawner) {
        let p = embassy_stm32::init(Config::default());

        // 1. 初始化GPIO引脚用于方向控制
        let ain1 = Output::new(p.PA0, Level::Low, Speed::Low);
        let ain2 = Output::new(p.PA1, Level::Low, Speed::Low);
        let bin1 = Output::new(p.PA2, Level::Low, Speed::Low);
        let bin2 = Output::new(p.PA3, Level::Low, Speed::Low);

        // 2. 初始化PWM引脚
        let ch1 = PwmPin::new(p.PA6, embassy_stm32::gpio::OutputType::PushPull);
        let ch2 = PwmPin::new(p.PA7, embassy_stm32::gpio::OutputType::PushPull);
        
        // 3. 创建SimplePwm实例
        let mut pwm = SimplePwm::new(
            p.TIM3, 
            Some(ch1), 
            Some(ch2), 
            None, 
            None, 
            Hertz(10_000), 
            Default::default()
        );
        
        // 4. 获取PWM通道 - 这些实现了SetDutyCycle trait
        let pwm_a = pwm.ch1();
        let pwm_b = pwm.ch2();

        // 5. 创建DRV8833驱动实例
        let mut motor_driver = Drv8833::new(ain1, ain2, bin1, bin2, pwm_a, pwm_b);

        // 6. 控制电机
        loop {
            // 电机A前进，电机B后退，50%速度
            motor_driver.set_motor(Motor::A, Direction::Forward, 50).unwrap();
            motor_driver.set_motor(Motor::B, Direction::Backward, 50).unwrap();
            Timer::after(Duration::from_secs(2)).await;

            // 停止所有电机
            motor_driver.stop_all().unwrap();
            Timer::after(Duration::from_secs(1)).await;
        }
    }
    */
    
    println!("Embassy 双电机控制示例 - 请参考上面的注释代码");
}

fn embassy_single_motor_example() {
    // 单电机控制示例
    
    /*
    // 在实际项目中：
    let ain1 = Output::new(p.PA0, Level::Low, Speed::Low);
    let ain2 = Output::new(p.PA1, Level::Low, Speed::Low);
    
    let ch1 = PwmPin::new(p.PA6, embassy_stm32::gpio::OutputType::PushPull);
    let mut pwm = SimplePwm::new(p.TIM3, Some(ch1), None, None, None, Hertz(10_000), Default::default());
    let pwm_ch1 = pwm.ch1();
    
    let mut motor = Drv8833Single::new(ain1, ain2, pwm_ch1);
    
    // 控制电机
    motor.set_motor(Direction::Forward, 75).unwrap();
    motor.set_motor(Direction::Backward, 50).unwrap();
    motor.brake().unwrap();
    motor.stop().unwrap();
    */
    
    println!("Embassy 单电机控制示例 - 请参考上面的注释代码");
}

fn main() {
    println!("DRV8833 Embassy 使用指南");
    println!("========================");
    println!();
    
    println!("关键要点：");
    println!("1. 使用 PwmPin::new() 创建PWM引脚");
    println!("2. 使用 SimplePwm::new() 创建PWM实例");
    println!("3. 使用 pwm.ch1(), pwm.ch2() 获取PWM通道");
    println!("4. PWM通道自动实现了 SetDutyCycle trait");
    println!("5. 直接传递给 Drv8833::new() 即可");
    println!();
    
    embassy_dual_motor_example();
    embassy_single_motor_example();
    
    println!();
    println!("硬件连接：");
    println!("STM32F103    DRV8833");
    println!("---------    -------");
    println!("PA0      ->  AIN1");
    println!("PA1      ->  AIN2");
    println!("PA2      ->  BIN1");
    println!("PA3      ->  BIN2");
    println!("PA6      ->  PWM A (TIM3_CH1)");
    println!("PA7      ->  PWM B (TIM3_CH2)");
    println!("3.3V     ->  VCC");
    println!("GND      ->  GND");
}
