//! Basic encoder usage example
//! 
//! This example demonstrates how to use the quadrature encoder driver
//! with mock GPIO pins for testing and development.

#![no_std]

use motor_encoder::encoder::{QuadratureEncoder, EncoderConfig, DecodingMode, Direction};

// Mock GPIO pin implementation for testing
struct MockPin {
    state: bool,
}

impl MockPin {
    fn new(initial_state: bool) -> Self {
        Self { state: initial_state }
    }
    
    fn set_state(&mut self, state: bool) {
        self.state = state;
    }
}

impl embedded_hal::digital::InputPin for MockPin {
    fn is_high(&mut self) -> Result<bool, Self::Error> {
        Ok(self.state)
    }
    
    fn is_low(&mut self) -> Result<bool, Self::Error> {
        Ok(!self.state)
    }
}

impl embedded_hal::digital::ErrorType for MockPin {
    type Error = ();
}

fn basic_encoder_example() {
    // Create mock pins for A and B phases
    let pin_a = MockPin::new(false);
    let pin_b = MockPin::new(false);
    
    // Create encoder configuration
    let config = EncoderConfig::new()
        .with_decoding_mode(DecodingMode::X4)
        .with_pulses_per_revolution(1000)
        .with_velocity_window_ms(100);
    
    // Create encoder instance
    let mut encoder = QuadratureEncoder::new(pin_a, pin_b, config);
    
    // Simulate encoder movement
    simulate_encoder_rotation(&mut encoder);
    
    // Read final position and direction
    let position = encoder.position();
    let direction = encoder.direction();
    let revolutions = encoder.position_revolutions();
    
    // In a real application, you would use defmt::info! or similar
    // For this example, we'll just demonstrate the API usage
    let _ = (position, direction, revolutions);
}

fn simulate_encoder_rotation(encoder: &mut QuadratureEncoder<MockPin, MockPin>) {
    // Simulate a few encoder state transitions for clockwise rotation
    // State sequence for clockwise: 00 -> 01 -> 11 -> 10 -> 00
    
    // This is a simplified simulation - in real usage, the process_edge()
    // method would be called from interrupt handlers
    
    // Start at 00
    encoder.update_state().ok();
    
    // Transition to 01 (B goes high)
    // Note: In real usage, you wouldn't directly manipulate pins like this
    // This is just for demonstration purposes
    let _ = encoder.process_edge();
    
    // Transition to 11 (A goes high)
    let _ = encoder.process_edge();
    
    // Transition to 10 (B goes low)
    let _ = encoder.process_edge();
    
    // Transition back to 00 (A goes low) - completes one full cycle
    let _ = encoder.process_edge();
}

fn configuration_examples() {
    // Example 1: High-resolution encoder with 4x decoding
    let _high_res_config = EncoderConfig::new()
        .with_decoding_mode(DecodingMode::X4)
        .with_pulses_per_revolution(4096)
        .with_velocity_window_ms(50)
        .with_timeout_ms(500)
        .with_error_checking(true);
    
    // Example 2: Simple encoder with 1x decoding
    let _simple_config = EncoderConfig::new()
        .with_decoding_mode(DecodingMode::X1)
        .with_pulses_per_revolution(100)
        .with_velocity_window_ms(200)
        .with_error_checking(false);
    
    // Example 3: Medium resolution with 2x decoding
    let _medium_config = EncoderConfig::new()
        .with_decoding_mode(DecodingMode::X2)
        .with_pulses_per_revolution(1000)
        .with_velocity_window_ms(100);
}

fn api_usage_examples() {
    let pin_a = MockPin::new(false);
    let pin_b = MockPin::new(false);
    let config = EncoderConfig::default();
    
    let encoder = QuadratureEncoder::new(pin_a, pin_b, config);
    
    // Position operations
    let _position = encoder.position();
    encoder.reset_position();
    encoder.set_position(1000);
    
    // Direction and movement
    let _direction = encoder.direction();
    let _revolutions = encoder.position_revolutions();
    
    // Error handling
    let _error_count = encoder.error_count();
    encoder.reset_error_count();
    
    // Configuration access
    let _config = encoder.config();
    
    #[cfg(feature = "embassy")]
    {
        // Velocity measurements (only available with Embassy timing)
        let _velocity_cps = encoder.velocity_cps();
        let _velocity_rpm = encoder.velocity_rpm();
    }
}

fn direction_detection_example() {
    let pin_a = MockPin::new(false);
    let pin_b = MockPin::new(false);
    let config = EncoderConfig::default();
    
    let encoder = QuadratureEncoder::new(pin_a, pin_b, config);
    
    // Check different direction states
    match encoder.direction() {
        Direction::Clockwise => {
            // Handle clockwise rotation
        },
        Direction::CounterClockwise => {
            // Handle counter-clockwise rotation
        },
        Direction::Stopped => {
            // Handle stopped state
        },
    }
}

fn error_handling_example() {
    let pin_a = MockPin::new(false);
    let pin_b = MockPin::new(false);
    
    // Enable error checking
    let config = EncoderConfig::new()
        .with_error_checking(true);
    
    let mut encoder = QuadratureEncoder::new(pin_a, pin_b, config);
    
    // Process encoder edge and handle potential errors
    match encoder.process_edge() {
        Ok(()) => {
            // Normal operation
        },
        Err(error) => {
            // Handle error based on type
            match error {
                motor_encoder::encoder::EncoderError::InvalidTransition => {
                    // Handle invalid state transition (possible noise)
                },
                motor_encoder::encoder::EncoderError::SignalTimeout => {
                    // Handle signal timeout
                },
                motor_encoder::encoder::EncoderError::GpioError => {
                    // Handle GPIO read error
                },
                motor_encoder::encoder::EncoderError::InvalidConfiguration => {
                    // Handle configuration error
                },
            }
        }
    }
}

// Main function for demonstration
fn main() {
    basic_encoder_example();
    configuration_examples();
    api_usage_examples();
    direction_detection_example();
    error_handling_example();
}
