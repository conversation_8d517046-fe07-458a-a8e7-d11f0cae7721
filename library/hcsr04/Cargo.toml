[package]
name = "hcsr04"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A no_std driver for the HC-SR04 ultrasonic distance sensor"
documentation = "https://docs.rs/hcsr04"
repository = "https://github.com/yourusername/hcsr04-rs"
license = "MIT OR Apache-2.0"
keywords = ["embedded", "sensor", "ultrasonic", "hc-sr04", "no-std"]
categories = ["embedded", "hardware-support", "no-std"]
readme = "README.md"

[dependencies]
embedded-hal = "1.0.0"
embassy-time = { version = "0.4.0", features = [
    "defmt",
    "defmt-timestamp-uptime",
    "tick-hz-32_768",
], git = "https://github.com/embassy-rs/embassy.git", optional = false}
