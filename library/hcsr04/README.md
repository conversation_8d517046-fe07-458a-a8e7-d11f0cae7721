# HCSR04 Ultrasonic Distance Sensor Driver for Embassy

A simple, blocking driver for the HC-SR04 ultrasonic distance sensor, specifically designed for embassy-stm32 with accurate timing.

## Features

- **Embassy-time integration** - High-precision timing using embassy-time
- **Blocking API** - Simple synchronous measurement function
- **No-std support** - Perfect for embedded systems
- **Configurable timeout** - Adjustable measurement timeout
- **Error handling** - Comprehensive error types
- **Range validation** - Automatic validation of measurement range (2-400cm)

## Hardware Requirements

- HC-SR04 ultrasonic distance sensor
- 2 GPIO pins (one output for trigger, one input for echo)
- 5V power supply for the sensor (3.3V may work but not guaranteed)

## Wiring

```
HC-SR04    MCU
-------    ---
VCC    ->  5V
GND    ->  GND
Trig   ->  GPIO Output Pin
Echo   ->  GPIO Input Pin
```

## Usage

### Basic Usage

Add this to your `Cargo.toml`:

```toml
[dependencies]
hcsr04 = "0.1.0"
embedded-hal = "1.0.0"
```

### Basic Usage

```rust
use hcsr04::Hcsr04;
use embassy_stm32::gpio::{Level, Output, Input, Pull, Speed};
use embassy_time::Duration;

// Initialize pins
let trigger = Output::new(p.PA0, Level::Low, Speed::Low);
let echo = Input::new(p.PA1, Pull::None);

// Create sensor instance
let mut sensor = Hcsr04::new(trigger, echo);

// Measure distance
match sensor.measure_distance() {
    Ok(distance_cm) => {
        defmt::println!("Distance: {:.1} cm", distance_cm);
    }
    Err(e) => {
        defmt::println!("Measurement error: {:?}", e);
    }
}
```

### With Custom Timeout

```rust
use embassy_time::Duration;

// Create sensor with custom timeout
let mut sensor = Hcsr04::new_with_timeout(trigger, echo, Duration::from_millis(50));

// Or set timeout later
sensor.set_timeout(Duration::from_millis(25));
```

## API Reference

### Main Types

- `Hcsr04<TRIGGER, ECHO>` - Main sensor driver struct
- `Error<TriggerError, EchoError>` - Error type for measurement failures

### Methods

- `new(trigger, echo)` - Create new sensor instance
- `new_with_timeout(trigger, echo, timeout)` - Create with custom timeout
- `measure_distance()` - Perform distance measurement (returns cm)
- `set_timeout(timeout)` - Set measurement timeout
- `timeout()` - Get current timeout setting

## Error Handling

The driver provides comprehensive error handling:

```rust
use hcsr04::Error;

match sensor.measure_distance() {
    Ok(distance) => println!("Distance: {:.1} cm", distance),
    Err(Error::Timeout) => println!("No echo received - check connections"),
    Err(Error::InvalidMeasurement) => println!("Object too close or too far"),
    Err(Error::Trigger(e)) => println!("Trigger pin error: {:?}", e),
    Err(Error::Echo(e)) => println!("Echo pin error: {:?}", e),
}
```

## Measurement Range

- **Minimum distance**: 2 cm
- **Maximum distance**: 400 cm (theoretical)
- **Practical range**: 2-300 cm for reliable measurements
- **Resolution**: ~0.3 cm (limited by sound speed and timing precision)

## Timing Specifications

- **Trigger pulse**: 10 μs high pulse
- **Echo pulse**: 150 μs to 25 ms (corresponding to 2.5 cm to 4.3 m)
- **Default timeout**: 30 ms (allows ~5m max range)
- **Measurement frequency**: Recommended max 10 Hz to avoid interference

## Performance Notes

- Uses embassy-time for microsecond-precision timing
- Blocking implementation suitable for simple applications
- Measurement accuracy depends on:
  - System clock accuracy
  - Temperature (affects sound speed)
  - Air humidity and pressure
  - Surface reflectivity of target object

## Troubleshooting

### No measurements / Always timeout

- Check wiring connections
- Ensure 5V power supply to sensor
- Verify trigger and echo pins are correct
- Check if there's an object within range

### Inconsistent measurements

- Ensure stable power supply
- Add delay between measurements (>60ms recommended)
- Check for electromagnetic interference
- Verify target surface is reflective enough

### Measurements too high/low

- Calibrate for temperature if precision is critical
- Check system clock accuracy
- Ensure no obstacles in sensor's path

## License

Licensed under either of

- Apache License, Version 2.0 ([LICENSE-APACHE](LICENSE-APACHE) or http://www.apache.org/licenses/LICENSE-2.0)
- MIT license ([LICENSE-MIT](LICENSE-MIT) or http://opensource.org/licenses/MIT)

at your option.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
