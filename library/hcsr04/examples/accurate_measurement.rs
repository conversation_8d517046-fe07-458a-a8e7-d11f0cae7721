//! 准确测量示例 - 解决测量不准问题
//! 
//! 这个示例展示了如何获得准确的HCSR04测量结果

#![no_std]

// 根据你的平台导入相应的库
// use embassy_stm32::gpio::{Level, Output, Input, Pull, Speed};
// use embassy_time::Delay;
// use hcsr04::{Hcsr04, Error};

/// 方法1：使用Embassy高精度版本（推荐）
#[cfg(feature = "embassy")]
fn method1_embassy_precision() {
    // let p = embassy_stm32::init(Default::default());
    // let trigger = Output::new(p.PA0, Level::Low, Speed::Low);
    // let echo = Input::new(p.PA1, Pull::None);
    
    // // 使用embassy高精度版本
    // let mut sensor = Hcsr04::new_embassy(trigger, echo);
    
    // // 高精度测量
    // match sensor.measure_distance_precise() {
    //     Ok(distance_cm) => {
    //         defmt::println!("精确距离: {:.2} cm", distance_cm);
    //     }
    //     Err(e) => {
    //         defmt::println!("测量错误: {:?}", e);
    //     }
    // }
}

/// 方法2：使用延迟基础测量（更稳定）
fn method2_delay_based() {
    // let p = your_platform::init(Default::default());
    // let trigger = Output::new(p.PA0, Level::Low, Speed::Low);
    // let echo = Input::new(p.PA1, Pull::None);
    // let delay = Delay;
    
    // let mut sensor = Hcsr04::new(trigger, echo, delay);
    
    // // 使用延迟基础测量（更稳定但精度稍低）
    // match sensor.measure_distance_with_delay_timing() {
    //     Ok(distance_cm) => {
    //         println!("距离: {:.1} cm", distance_cm);
    //     }
    //     Err(e) => {
    //         println!("测量错误: {:?}", e);
    //     }
    // }
}

/// 方法3：校准基础实现
fn method3_calibration() {
    // let p = your_platform::init(Default::default());
    // let trigger = Output::new(p.PA0, Level::Low, Speed::Low);
    // let echo = Input::new(p.PA1, Pull::None);
    // let delay = Delay;
    
    // let mut sensor = Hcsr04::new(trigger, echo, delay);
    
    // // 步骤1：根据你的平台设置初始校准因子
    // // STM32F4 (168MHz): 0.1-0.2
    // // STM32F1 (72MHz): 0.2-0.5
    // // 其他平台: 从0.2开始
    // sensor.set_timing_calibration(0.2);
    
    // // 步骤2：自动校准（将物体放在已知距离，如20cm）
    // println!("请将物体放置在20cm处进行校准...");
    // match sensor.auto_calibrate(20.0, 5) {
    //     Ok(new_factor) => {
    //         println!("校准成功！新校准因子: {:.3}", new_factor);
    //     }
    //     Err(e) => {
    //         println!("校准失败: {:?}", e);
    //         // 手动调整校准因子
    //         sensor.set_timing_calibration(0.4); // 尝试不同的值
    //     }
    // }
    
    // // 步骤3：验证校准结果
    // for distance in [5.0, 10.0, 15.0, 20.0, 30.0] {
    //     println!("请将物体放置在{}cm处，按回车继续...", distance);
    //     // 等待用户输入...
    //     
    //     match sensor.measure_distance() {
    //         Ok(measured) => {
    //             let error = ((measured - distance) / distance * 100.0).abs();
    //             println!("期望: {}cm, 测量: {:.1}cm, 误差: {:.1}%", 
    //                      distance, measured, error);
    //         }
    //         Err(e) => {
    //             println!("测量失败: {:?}", e);
    //         }
    //     }
    // }
}

/// 方法4：多次测量平均（提高稳定性）
fn method4_averaging() {
    // let mut sensor = Hcsr04::new(trigger, echo, delay);
    
    // // 多次测量求平均
    // let samples = 5;
    // let mut total = 0.0;
    // let mut count = 0;
    
    // for _ in 0..samples {
    //     match sensor.measure_distance() {
    //         Ok(distance) => {
    //             total += distance;
    //             count += 1;
    //         }
    //         Err(_) => {
    //             // 跳过失败的测量
    //         }
    //     }
    //     // 等待60ms避免声波干扰
    //     delay.delay_ms(60);
    // }
    
    // if count > 0 {
    //     let average = total / count as f32;
    //     println!("平均距离: {:.1} cm (基于{}次测量)", average, count);
    // } else {
    //     println!("所有测量都失败了");
    // }
}

/// 方法5：异常值过滤
fn method5_filtering() {
    // let mut sensor = Hcsr04::new(trigger, echo, delay);
    
    // // 收集多个测量值
    // let mut measurements = Vec::new();
    // for _ in 0..7 {
    //     if let Ok(distance) = sensor.measure_distance() {
    //         measurements.push(distance);
    //     }
    //     delay.delay_ms(60);
    // }
    
    // if measurements.len() >= 3 {
    //     // 排序
    //     measurements.sort_by(|a, b| a.partial_cmp(b).unwrap());
    //     
    //     // 去除最大和最小值，计算平均值
    //     let filtered: Vec<f32> = measurements[1..measurements.len()-1].to_vec();
    //     let average = filtered.iter().sum::<f32>() / filtered.len() as f32;
    //     
    //     println!("过滤后距离: {:.1} cm", average);
    //     println!("原始测量值: {:?}", measurements);
    //     println!("过滤后值: {:?}", filtered);
    // }
}

/// 完整的测量流程示例
fn complete_measurement_example() {
    // 这是一个完整的测量流程，结合了多种技术
    
    // 1. 初始化传感器
    // let mut sensor = Hcsr04::new(trigger, echo, delay);
    
    // 2. 设置适合你平台的校准因子
    // sensor.set_timing_calibration(0.2); // 根据你的MCU调整
    
    // 3. 可选：进行自动校准
    // if let Ok(factor) = sensor.auto_calibrate(15.0, 3) {
    //     println!("自动校准完成，新因子: {:.3}", factor);
    // }
    
    // 4. 进行稳定的测量
    // loop {
    //     // 使用多次测量平均
    //     let mut valid_measurements = Vec::new();
    //     
    //     for _ in 0..5 {
    //         match sensor.measure_distance() {
    //             Ok(distance) => {
    //                 // 基本范围检查
    //                 if distance >= 2.0 && distance <= 400.0 {
    //                     valid_measurements.push(distance);
    //                 }
    //             }
    //             Err(_) => {
    //                 // 跳过失败的测量
    //             }
    //         }
    //         delay.delay_ms(60); // 避免声波干扰
    //     }
    //     
    //     if !valid_measurements.is_empty() {
    //         // 计算平均值
    //         let average = valid_measurements.iter().sum::<f32>() / valid_measurements.len() as f32;
    //         
    //         // 计算标准差来评估测量稳定性
    //         let variance = valid_measurements.iter()
    //             .map(|x| (x - average).powi(2))
    //             .sum::<f32>() / valid_measurements.len() as f32;
    //         let std_dev = variance.sqrt();
    //         
    //         println!("距离: {:.1} cm ±{:.1} cm ({}次测量)", 
    //                  average, std_dev, valid_measurements.len());
    //         
    //         // 如果标准差太大，说明测量不稳定
    //         if std_dev > 2.0 {
    //             println!("警告: 测量不稳定，检查环境和连接");
    //         }
    //     } else {
    //         println!("无有效测量，检查传感器连接");
    //     }
    //     
    //     delay.delay_ms(500); // 每500ms测量一次
    // }
}

/// 故障排除函数
fn troubleshooting() {
    // 这个函数帮助诊断常见问题
    
    // let mut sensor = Hcsr04::new(trigger, echo, delay);
    
    // println!("开始HCSR04故障排除...");
    
    // // 测试1：基本连接测试
    // println!("测试1: 基本连接");
    // match sensor.measure_distance() {
    //     Ok(distance) => {
    //         println!("✓ 基本连接正常，测量值: {:.1} cm", distance);
    //     }
    //     Err(Error::Timeout) => {
    //         println!("✗ 超时 - 检查接线和供电");
    //         return;
    //     }
    //     Err(e) => {
    //         println!("✗ 其他错误: {:?}", e);
    //         return;
    //     }
    // }
    
    // // 测试2：稳定性测试
    // println!("测试2: 稳定性测试 (10次测量)");
    // let mut measurements = Vec::new();
    // for i in 0..10 {
    //     match sensor.measure_distance() {
    //         Ok(distance) => {
    //             measurements.push(distance);
    //             println!("  测量{}: {:.1} cm", i+1, distance);
    //         }
    //         Err(_) => {
    //             println!("  测量{}: 失败", i+1);
    //         }
    //     }
    //     delay.delay_ms(100);
    // }
    
    // if !measurements.is_empty() {
    //     let avg = measurements.iter().sum::<f32>() / measurements.len() as f32;
    //     let max = measurements.iter().fold(0.0f32, |a, &b| a.max(b));
    //     let min = measurements.iter().fold(f32::INFINITY, |a, &b| a.min(b));
    //     
    //     println!("  平均: {:.1} cm", avg);
    //     println!("  范围: {:.1} - {:.1} cm", min, max);
    //     println!("  变化: {:.1} cm", max - min);
    //     
    //     if (max - min) > 5.0 {
    //         println!("  ⚠ 测量变化较大，可能需要校准或检查环境");
    //     } else {
    //         println!("  ✓ 测量相对稳定");
    //     }
    // }
    
    // println!("故障排除完成");
}

fn main() {
    // 这是一个模板文件，实际使用时需要：
    // 1. 根据你的平台导入正确的库
    // 2. 初始化GPIO引脚和延迟
    // 3. 取消注释相应的代码
    // 4. 根据你的MCU调整校准因子
    
    println!("HCSR04准确测量示例");
    println!("请参考各个方法的注释，选择适合你的解决方案");
}
