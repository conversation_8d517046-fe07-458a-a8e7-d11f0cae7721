//! Embassy HCSR04 example
//!
//! This example shows how to use the HCSR04 driver with embassy-stm32.

#![no_std]

// Uncomment these for actual use:
// use embassy_stm32::gpio::{Level, Output, Input, Pull, Speed};
// use embassy_time::Duration;
// use hcsr04::{Hcsr04, Error};

/// Basic usage example with embassy-stm32
fn embassy_example() {
    // Initialize your platform
    // let p = embassy_stm32::init(Default::default());

    // Initialize GPIO pins
    // let trigger = Output::new(p.PA0, Level::Low, Speed::Low);
    // let echo = Input::new(p.PA1, Pull::None);

    // Create sensor instance
    // let mut sensor = Hcsr04::new(trigger, echo);

    // Optional: set custom timeout
    // sensor.set_timeout(Duration::from_millis(50));

    // Measure distance
    // match sensor.measure_distance() {
    //     Ok(distance_cm) => {
    //         defmt::println!("Distance: {:.1} cm", distance_cm);
    //
    //         // Classify distance
    //         if distance_cm < 10.0 {
    //             defmt::println!("Object very close!");
    //         } else if distance_cm < 50.0 {
    //             defmt::println!("Object nearby");
    //         } else {
    //             defmt::println!("Object far away");
    //         }
    //     }
    //     Err(Error::Timeout) => {
    //         defmt::println!("Timeout - check connections and power");
    //     }
    //     Err(Error::InvalidMeasurement) => {
    //         defmt::println!("Invalid measurement - object too close/far");
    //     }
    //     Err(Error::Trigger(e)) => {
    //         defmt::println!("Trigger pin error: {:?}", e);
    //     }
    //     Err(Error::Echo(e)) => {
    //         defmt::println!("Echo pin error: {:?}", e);
    //     }
    // }
}

fn main() {
    // This is a template example file
    // Uncomment and adapt the embassy_example function above for your use case
}
