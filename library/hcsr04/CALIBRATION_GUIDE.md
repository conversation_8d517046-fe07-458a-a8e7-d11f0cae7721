# HCSR04 测量校准指南

## 问题诊断

如果你发现HCSR04测量的值完全不准，这通常是由于时间测量不准确造成的。本指南将帮你解决这个问题。

## 原因分析

### 1. 基础实现的限制
基础的`measure_distance()`方法使用忙等待循环计时，这种方法的准确性取决于：
- CPU频率
- 编译器优化
- 系统负载
- 中断处理

### 2. 常见问题
- **测量值偏小**：时间计算过快，校准因子需要增大
- **测量值偏大**：时间计算过慢，校准因子需要减小
- **测量不稳定**：系统中断或负载影响计时

## 解决方案

### 方案1：使用Embassy高精度版本（推荐）

如果你使用embassy-stm32，强烈建议使用高精度版本：

```rust
// 在Cargo.toml中启用embassy特性
[dependencies]
hcsr04 = { version = "0.1.0", features = ["embassy"] }

// 使用高精度测量
let mut sensor = Hcsr04::new_embassy(trigger, echo);
let distance = sensor.measure_distance_precise()?;
```

### 方案2：使用延迟基础的测量

这种方法更稳定，但精度较低：

```rust
let distance = sensor.measure_distance_with_delay_timing()?;
```

### 方案3：校准基础实现

#### 步骤1：手动校准

```rust
// 1. 放置一个已知距离的物体（如20cm）
// 2. 测量几次并记录平均值
let mut total = 0.0;
for _ in 0..10 {
    if let Ok(distance) = sensor.measure_distance() {
        total += distance;
    }
    delay.delay_ms(100);
}
let average_measured = total / 10.0;

// 3. 计算校准因子
let known_distance = 20.0; // 实际距离
let current_factor = sensor.timing_calibration();
let new_factor = current_factor * (known_distance / average_measured);

// 4. 应用新的校准因子
sensor.set_timing_calibration(new_factor);
```

#### 步骤2：自动校准

```rust
// 放置物体在已知距离（如15cm），然后运行自动校准
match sensor.auto_calibrate(15.0, 5) {
    Ok(factor) => {
        defmt::println!("校准成功！新因子: {:.3}", factor);
    }
    Err(e) => {
        defmt::println!("校准失败: {:?}", e);
    }
}
```

## 平台特定的校准因子

### STM32F4系列（168MHz）
```rust
sensor.set_timing_calibration(0.1); // 起始值
```

### STM32F1系列（72MHz）
```rust
sensor.set_timing_calibration(0.3); // 起始值
```

### STM32F0系列（48MHz）
```rust
sensor.set_timing_calibration(0.5); // 起始值
```

### 其他平台
从0.2开始，根据实际测量调整。

## 校准过程详解

### 1. 准备工作
- 准备一把精确的尺子
- 选择一个平坦、反射良好的表面作为目标
- 确保传感器稳定安装

### 2. 测量步骤
```rust
// 测试不同距离的准确性
let test_distances = [5.0, 10.0, 15.0, 20.0, 30.0]; // cm

for &expected in &test_distances {
    println!("请将物体放置在{}cm处，按回车继续...", expected);
    // 等待用户输入...
    
    let mut measurements = Vec::new();
    for _ in 0..10 {
        if let Ok(distance) = sensor.measure_distance() {
            measurements.push(distance);
        }
        delay.delay_ms(100);
    }
    
    let average = measurements.iter().sum::<f32>() / measurements.len() as f32;
    let error = ((average - expected) / expected * 100.0).abs();
    
    println!("期望: {}cm, 测量: {:.1}cm, 误差: {:.1}%", 
             expected, average, error);
}
```

### 3. 验证校准
校准后，重复测量几个已知距离，确保误差在±5%以内。

## 高级技巧

### 1. 温度补偿
声速随温度变化，如果需要高精度：

```rust
// 根据温度调整声速（可选）
let temperature_celsius = 25.0; // 当前温度
let sound_speed_adjusted = 331.3 + 0.606 * temperature_celsius; // m/s
// 然后相应调整校准因子
```

### 2. 多次测量平均
```rust
fn measure_average(sensor: &mut Hcsr04<_, _, _>, samples: u8) -> Result<f32, _> {
    let mut total = 0.0;
    let mut count = 0;
    
    for _ in 0..samples {
        if let Ok(distance) = sensor.measure_distance() {
            total += distance;
            count += 1;
        }
        // 等待60ms避免声波干扰
        delay.delay_ms(60);
    }
    
    if count > 0 {
        Ok(total / count as f32)
    } else {
        Err(Error::Timeout)
    }
}
```

### 3. 异常值过滤
```rust
fn measure_filtered(sensor: &mut Hcsr04<_, _, _>) -> Result<f32, _> {
    let mut measurements = Vec::new();
    
    // 收集多个测量值
    for _ in 0..5 {
        if let Ok(distance) = sensor.measure_distance() {
            measurements.push(distance);
        }
        delay.delay_ms(60);
    }
    
    if measurements.is_empty() {
        return Err(Error::Timeout);
    }
    
    // 排序并移除异常值
    measurements.sort_by(|a, b| a.partial_cmp(b).unwrap());
    
    // 使用中位数或去除极值后的平均值
    if measurements.len() >= 3 {
        // 去除最大和最小值，计算平均值
        let sum: f32 = measurements[1..measurements.len()-1].iter().sum();
        Ok(sum / (measurements.len() - 2) as f32)
    } else {
        Ok(measurements[measurements.len() / 2])
    }
}
```

## 故障排除

### 问题1：始终超时
- 检查接线
- 确保5V供电
- 检查引脚配置

### 问题2：测量值跳动很大
- 增加测量间隔（>60ms）
- 检查电源稳定性
- 避免电磁干扰

### 问题3：短距离测量不准
- HC-SR04最小测量距离约2cm
- 确保目标表面足够大且平整

### 问题4：长距离测量不准
- 检查目标表面反射性
- 增加超时时间
- 考虑环境噪声影响

## 总结

1. **优先使用embassy高精度版本**
2. **如果必须使用基础版本，进行校准**
3. **使用多次测量平均值**
4. **注意测量间隔和环境因素**

通过正确的校准和使用方法，HCSR04可以达到±1cm的测量精度。
