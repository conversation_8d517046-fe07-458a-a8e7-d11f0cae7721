#![no_std]

//! # HCSR04 Ultrasonic Distance Sensor Driver for Embassy
//!
//! A simple, blocking driver for the HC-SR04 ultrasonic distance sensor,
//! specifically designed for embassy-stm32 with accurate timing.
//!
//! ## Example
//!
//! ```rust,no_run
//! use hcsr04::Hcsr04;
//! use embassy_stm32::gpio::{Level, Output, Input, Pull, Speed};
//!
//! // Initialize pins
//! let trigger = Output::new(p.PA0, Level::Low, Speed::Low);
//! let echo = Input::new(p.PA1, Pull::None);
//!
//! // Create sensor instance
//! let mut sensor = Hcsr04::new(trigger, echo);
//!
//! // Measure distance
//! match sensor.measure_distance() {
//!     Ok(distance_cm) => {
//!         defmt::println!("Distance: {:.1} cm", distance_cm);
//!     }
//!     Err(e) => {
//!         defmt::println!("Error: {:?}", e);
//!     }
//! }
//! ```

use embassy_time::{Duration, Instant};
use embedded_hal::digital::{InputPin, OutputPin};

/// Default timeout for echo pulse measurement
pub const DEFAULT_TIMEOUT: Duration = Duration::from_millis(30);

/// Speed of sound in air at 20°C in cm/μs
const SOUND_SPEED_CM_PER_US: f32 = 0.0343;

/// Errors that can occur during distance measurement
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Error<TriggerError, EchoError> {
    /// Trigger pin error
    Trigger(TriggerError),
    /// Echo pin error
    Echo(EchoError),
    /// Timeout waiting for echo pulse
    Timeout,
    /// Invalid measurement (too short or too long)
    InvalidMeasurement,
}

/// HC-SR04 ultrasonic distance sensor driver for Embassy
pub struct Hcsr04<TRIGGER, ECHO> {
    trigger: TRIGGER,
    echo: ECHO,
    timeout: Duration,
}

impl<TRIGGER, ECHO> Hcsr04<TRIGGER, ECHO>
where
    TRIGGER: OutputPin,
    ECHO: InputPin,
{
    /// Create a new HC-SR04 sensor instance
    ///
    /// # Arguments
    ///
    /// * `trigger` - Output pin connected to the trigger pin of HC-SR04
    /// * `echo` - Input pin connected to the echo pin of HC-SR04
    ///
    /// # Returns
    ///
    /// A new `Hcsr04` instance with default timeout
    pub fn new(trigger: TRIGGER, echo: ECHO) -> Self {
        Self {
            trigger,
            echo,
            timeout: DEFAULT_TIMEOUT,
        }
    }

    /// Create a new HC-SR04 sensor instance with custom timeout
    ///
    /// # Arguments
    ///
    /// * `trigger` - Output pin connected to the trigger pin of HC-SR04
    /// * `echo` - Input pin connected to the echo pin of HC-SR04
    /// * `timeout` - Timeout duration for echo pulse measurement
    ///
    /// # Returns
    ///
    /// A new `Hcsr04` instance with custom timeout
    pub fn new_with_timeout(trigger: TRIGGER, echo: ECHO, timeout: Duration) -> Self {
        Self {
            trigger,
            echo,
            timeout,
        }
    }

    /// Measure distance in centimeters using embassy-time for accurate timing
    ///
    /// This function performs a blocking distance measurement by:
    /// 1. Sending a 10μs trigger pulse
    /// 2. Waiting for the echo pulse to start
    /// 3. Measuring the duration of the echo pulse using embassy-time
    /// 4. Converting the time to distance
    ///
    /// # Returns
    ///
    /// * `Ok(distance)` - Distance in centimeters (2.0 to 400.0 cm)
    /// * `Err(Error)` - Various error conditions
    ///
    /// # Errors
    ///
    /// * `Error::Trigger` - Failed to control trigger pin
    /// * `Error::Echo` - Failed to read echo pin
    /// * `Error::Timeout` - No echo received within timeout period
    /// * `Error::InvalidMeasurement` - Measurement outside valid range
    pub fn measure_distance(&mut self) -> Result<f32, Error<TRIGGER::Error, ECHO::Error>> {
        // Ensure trigger is low initially
        self.trigger.set_low().map_err(Error::Trigger)?;
        embassy_time::block_for(Duration::from_micros(2));

        // Send 10μs trigger pulse
        self.trigger.set_high().map_err(Error::Trigger)?;
        embassy_time::block_for(Duration::from_micros(10));
        self.trigger.set_low().map_err(Error::Trigger)?;

        // Wait for echo to go high (start of pulse)
        self.wait_for_echo_high()?;
        let start_time = Instant::now();

        // Wait for echo to go low (end of pulse)
        self.wait_for_echo_low()?;
        let end_time = Instant::now();

        // Calculate pulse duration in microseconds
        let pulse_duration = end_time.saturating_duration_since(start_time);
        let pulse_duration_us = pulse_duration.as_micros() as f32;

        // Convert to distance in centimeters
        // Distance = (pulse_duration * sound_speed) / 2
        // Division by 2 because sound travels to object and back
        let distance_cm = (pulse_duration_us * SOUND_SPEED_CM_PER_US) / 2.0;

        // Validate measurement range (2cm to 400cm for HC-SR04)
        if !(2.0..=400.0).contains(&distance_cm) {
            return Err(Error::InvalidMeasurement);
        }

        Ok(distance_cm)
    }

    /// Wait for echo pin to go high with timeout
    fn wait_for_echo_high(&mut self) -> Result<(), Error<TRIGGER::Error, ECHO::Error>> {
        let start_time = Instant::now();

        loop {
            let is_high = self.echo.is_high().map_err(Error::Echo)?;

            if is_high {
                return Ok(());
            }

            if start_time.elapsed() > self.timeout {
                return Err(Error::Timeout);
            }
        }
    }

    /// Wait for echo pin to go low with timeout
    fn wait_for_echo_low(&mut self) -> Result<(), Error<TRIGGER::Error, ECHO::Error>> {
        let start_time = Instant::now();

        loop {
            let is_high = self.echo.is_high().map_err(Error::Echo)?;

            if !is_high {
                return Ok(());
            }

            if start_time.elapsed() > self.timeout {
                return Err(Error::Timeout);
            }
        }
    }

    /// Set custom timeout for echo pulse measurement
    ///
    /// # Arguments
    ///
    /// * `timeout` - Timeout duration
    pub fn set_timeout(&mut self, timeout: Duration) {
        self.timeout = timeout;
    }

    /// Get current timeout setting
    ///
    /// # Returns
    ///
    /// Current timeout duration
    pub fn timeout(&self) -> Duration {
        self.timeout
    }
}
